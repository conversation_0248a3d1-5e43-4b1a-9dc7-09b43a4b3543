/* HuntGame - 3D中国象棋游戏主样式文件 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #2F1B14 0%, #1A0F0A 100%);
    color: #E8D5B7;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 加载界面样式 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2F1B14 0%, #1A0F0A 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: 2rem;
}

.loading-logo h1 {
    font-size: 3rem;
    color: #D4AF37;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.loading-logo p {
    font-size: 1.2rem;
    color: #B8860B;
    margin-bottom: 2rem;
}

.loading-progress {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #D4AF37 0%, #B8860B 100%);
    border-radius: 4px;
    width: 0%;
    transition: width 0.3s ease;
}

.loading-text {
    font-size: 1rem;
    color: #E8D5B7;
    opacity: 0.8;
}

/* 游戏容器样式 */
.game-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.game-canvas {
    display: block;
    width: 100%;
    height: 100%;
    background: transparent;
}

/* 游戏HUD界面样式 */
.game-hud {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
}

.game-hud > * {
    pointer-events: auto;
}

/* 顶部状态栏 */
.top-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(180deg, rgba(47, 27, 20, 0.9) 0%, rgba(47, 27, 20, 0.7) 100%);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.game-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.current-player {
    font-size: 1.2rem;
    font-weight: bold;
    color: #D4AF37;
}

.game-status {
    font-size: 1rem;
    color: #E8D5B7;
    opacity: 0.8;
}

.game-controls {
    display: flex;
    gap: 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.btn-primary {
    background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
    color: #2F1B14;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #E6C547 0%, #C9971B 100%);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
    transform: translateY(-1px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #E8D5B7;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.btn-secondary:hover {
    background: rgba(212, 175, 55, 0.2);
    border-color: rgba(212, 175, 55, 0.5);
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* 侧边控制面板 */
.side-panel {
    position: absolute;
    top: 60px;
    right: -320px;
    width: 320px;
    height: calc(100% - 60px);
    background: linear-gradient(135deg, rgba(47, 27, 20, 0.95) 0%, rgba(26, 15, 10, 0.95) 100%);
    backdrop-filter: blur(15px);
    border-left: 1px solid rgba(212, 175, 55, 0.3);
    transition: right 0.3s ease;
    overflow-y: auto;
}

.side-panel.active {
    right: 0;
}

.panel-content {
    padding: 2rem;
}

.panel-content h3 {
    color: #D4AF37;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    color: #E8D5B7;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-select {
    width: 100%;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 4px;
    color: #E8D5B7;
    font-size: 0.9rem;
}

.form-select:focus {
    outline: none;
    border-color: #D4AF37;
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.setting-group input[type="checkbox"] {
    margin-right: 0.5rem;
    accent-color: #D4AF37;
}

.panel-actions {
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 对话框样式 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.dialog-content {
    background: linear-gradient(135deg, #2F1B14 0%, #1A0F0A 100%);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 12px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.dialog-title {
    color: #D4AF37;
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.dialog-message {
    color: #E8D5B7;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.dialog-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* 工具提示样式 */
.tooltip {
    position: absolute;
    background: rgba(47, 27, 20, 0.95);
    color: #E8D5B7;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.8rem;
    pointer-events: none;
    z-index: 1001;
    border: 1px solid rgba(212, 175, 55, 0.3);
    backdrop-filter: blur(10px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(212, 175, 55, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(212, 175, 55, 0.7);
}

/* 提示面板样式 */
.hint-panel {
    position: fixed;
    top: 80px;
    right: -400px;
    width: 380px;
    height: calc(100vh - 100px);
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid #D4AF37;
    border-radius: 15px 0 0 15px;
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.5);
    transition: right 0.3s ease;
}

.hint-panel.visible {
    right: 0;
}

.hint-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #D4AF37;
    background: rgba(212, 175, 55, 0.1);
}

.hint-title {
    color: #D4AF37;
    font-size: 1.2em;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.hint-close-btn {
    background: none;
    border: none;
    color: #E8D5B7;
    font-size: 1.2em;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.hint-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #D4AF37;
}

.hint-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.hint-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #E8D5B7;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(212, 175, 55, 0.3);
    border-top: 3px solid #D4AF37;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.position-evaluation {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.eval-score {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.eval-label {
    color: #E8D5B7;
    font-weight: bold;
}

.eval-value {
    color: #D4AF37;
    font-weight: bold;
}

.eval-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.eval-progress {
    height: 100%;
    background: #D4AF37;
    transition: width 0.5s ease;
}

.eval-progress.advantage {
    background: #4CAF50;
}

.eval-progress.disadvantage {
    background: #f44336;
}

.eval-progress.balanced {
    background: #D4AF37;
}

.main-hint {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 10px;
    border-left: 4px solid #D4AF37;
}

.hint-description {
    color: #E8D5B7;
    font-size: 1.1em;
    margin-bottom: 15px;
    line-height: 1.4;
}

.hint-confidence {
    display: flex;
    align-items: center;
    gap: 10px;
}

.confidence-label {
    color: #E8D5B7;
    font-size: 0.9em;
    min-width: 60px;
}

.confidence-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: #D4AF37;
    transition: width 0.5s ease;
}

.confidence-fill.high {
    background: #4CAF50;
}

.confidence-fill.medium {
    background: #D4AF37;
}

.confidence-fill.low {
    background: #ff9800;
}

.confidence-value {
    color: #E8D5B7;
    font-size: 0.9em;
    min-width: 35px;
    text-align: right;
}

.hint-details {
    margin-bottom: 20px;
}

.hint-tabs {
    display: flex;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    margin-bottom: 15px;
}

.hint-tab {
    flex: 1;
    padding: 10px 15px;
    background: none;
    border: none;
    color: #E8D5B7;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.hint-tab:hover {
    background: rgba(255, 255, 255, 0.05);
}

.hint-tab.active {
    color: #D4AF37;
    border-bottom-color: #D4AF37;
}

.hint-tab-content {
    min-height: 200px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.recommended-moves {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.recommended-move {
    display: flex;
    align-items: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.recommended-move:hover {
    background: rgba(212, 175, 55, 0.1);
    border-left-color: #D4AF37;
}

.move-rank {
    width: 30px;
    height: 30px;
    background: #D4AF37;
    color: #2F1B14;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9em;
    margin-right: 12px;
}

.move-info {
    flex: 1;
}

.move-description {
    color: #E8D5B7;
    font-weight: bold;
    margin-bottom: 4px;
}

.move-reasoning {
    color: rgba(232, 213, 183, 0.7);
    font-size: 0.85em;
    line-height: 1.3;
}

.move-score {
    color: #4CAF50;
    font-weight: bold;
    font-size: 0.9em;
}

.no-hints {
    text-align: center;
    color: rgba(232, 213, 183, 0.6);
    padding: 40px 20px;
    font-style: italic;
}

.position-analysis {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.analysis-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.analysis-label {
    color: #E8D5B7;
    font-weight: 500;
}

.analysis-value {
    color: #D4AF37;
    font-weight: bold;
}

.threats-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.threat-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid #f44336;
}

.threat-severity {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 1.2em;
}

.severity-high {
    background: rgba(244, 67, 54, 0.2);
}

.severity-medium {
    background: rgba(255, 152, 0, 0.2);
}

.severity-low {
    background: rgba(76, 175, 80, 0.2);
}

.threat-info {
    flex: 1;
}

.threat-description {
    color: #E8D5B7;
    font-weight: bold;
    margin-bottom: 4px;
}

.threat-position {
    color: rgba(232, 213, 183, 0.7);
    font-size: 0.85em;
}

.no-threats {
    text-align: center;
    color: rgba(232, 213, 183, 0.6);
    padding: 40px 20px;
    font-style: italic;
}

.hint-error {
    text-align: center;
    padding: 40px 20px;
}

.error-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.error-message {
    color: #E8D5B7;
    margin-bottom: 20px;
    font-size: 1.1em;
}

.retry-btn {
    background: #D4AF37;
    color: #2F1B14;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: #E6C547;
    transform: translateY(-1px);
}

.hint-actions {
    padding: 15px 20px;
    border-top: 1px solid rgba(212, 175, 55, 0.3);
    display: flex;
    gap: 10px;
}

.hint-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.hint-btn-primary {
    background: #D4AF37;
    color: #2F1B14;
}

.hint-btn-primary:hover {
    background: #E6C547;
    transform: translateY(-1px);
}

.hint-btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #E8D5B7;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.hint-btn-secondary:hover {
    background: rgba(212, 175, 55, 0.2);
    border-color: rgba(212, 175, 55, 0.5);
}

/* 历史面板样式 */
.history-panel {
    position: fixed;
    top: 80px;
    left: -450px;
    width: 420px;
    height: calc(100vh - 100px);
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid #D4AF37;
    border-radius: 0 15px 15px 0;
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 5px 0 20px rgba(0, 0, 0, 0.5);
    transition: left 0.3s ease;
}

.history-panel.visible {
    left: 0;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #D4AF37;
    background: rgba(212, 175, 55, 0.1);
}

.history-title {
    color: #D4AF37;
    font-size: 1.2em;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.history-actions {
    display: flex;
    gap: 8px;
}

.history-btn {
    background: none;
    border: none;
    color: #E8D5B7;
    font-size: 1.1em;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.history-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #D4AF37;
}

.history-close-btn {
    background: none;
    border: none;
    color: #E8D5B7;
    font-size: 1.2em;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.history-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #D4AF37;
}

.history-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.history-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    color: rgba(232, 213, 183, 0.7);
    font-size: 0.85em;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    color: #D4AF37;
    font-size: 1.2em;
    font-weight: bold;
}

.history-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.filter-select {
    flex: 1;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 6px;
    color: #E8D5B7;
    font-size: 0.9em;
}

.filter-select:focus {
    outline: none;
    border-color: #D4AF37;
}

.game-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.game-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.game-item:hover {
    background: rgba(212, 175, 55, 0.1);
    border-left-color: #D4AF37;
}

.game-info {
    flex: 1;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.game-date {
    color: #E8D5B7;
    font-weight: bold;
    font-size: 0.95em;
}

.game-result {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: bold;
}

.game-result.win {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.game-result.loss {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.game-result.draw {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.game-result.incomplete {
    background: rgba(158, 158, 158, 0.2);
    color: #9e9e9e;
}

.game-details {
    display: flex;
    gap: 12px;
    color: rgba(232, 213, 183, 0.7);
    font-size: 0.85em;
}

.game-actions {
    display: flex;
    gap: 6px;
}

.game-btn {
    background: none;
    border: none;
    color: rgba(232, 213, 183, 0.7);
    font-size: 1em;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.game-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #D4AF37;
}

.no-games {
    text-align: center;
    padding: 60px 20px;
    color: rgba(232, 213, 183, 0.6);
}

.no-games i {
    font-size: 3em;
    margin-bottom: 15px;
    display: block;
}

.no-games p {
    font-size: 1.1em;
    font-style: italic;
}

/* 复盘视图样式 */
.replay-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.replay-info {
    flex: 1;
    text-align: center;
}

.replay-title {
    color: #D4AF37;
    font-weight: bold;
    font-size: 1.1em;
}

.replay-controls {
    margin-bottom: 20px;
}

.replay-progress {
    margin-bottom: 15px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    color: #E8D5B7;
    font-size: 0.9em;
}

.progress-bar {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #D4AF37;
    transition: width 0.3s ease;
}

.replay-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 15px;
}

.replay-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    color: #E8D5B7;
    padding: 10px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.replay-btn:hover {
    background: rgba(212, 175, 55, 0.2);
    border-color: #D4AF37;
    color: #D4AF37;
}

.replay-settings {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #E8D5B7;
    font-size: 0.9em;
}

.replay-settings label {
    color: rgba(232, 213, 183, 0.7);
}

.replay-settings input[type="range"] {
    width: 100px;
    accent-color: #D4AF37;
}

.move-list {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    overflow: hidden;
}

.move-list-header {
    display: grid;
    grid-template-columns: 60px 1fr 60px;
    gap: 10px;
    padding: 12px 15px;
    background: rgba(212, 175, 55, 0.1);
    color: #D4AF37;
    font-weight: bold;
    font-size: 0.9em;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.move-list-content {
    max-height: 300px;
    overflow-y: auto;
}

.move-item {
    display: grid;
    grid-template-columns: 60px 1fr 60px;
    gap: 10px;
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

.move-item:hover {
    background: rgba(212, 175, 55, 0.1);
}

.move-item.active {
    background: rgba(212, 175, 55, 0.2);
    border-left: 3px solid #D4AF37;
}

.move-number {
    color: rgba(232, 213, 183, 0.7);
    font-size: 0.9em;
    text-align: center;
}

.move-notation {
    color: #E8D5B7;
    font-weight: bold;
}

.move-time {
    color: rgba(232, 213, 183, 0.7);
    font-size: 0.85em;
    text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hint-panel {
        width: 100%;
        right: -100%;
        border-radius: 0;
    }

    .hint-panel.visible {
        right: 0;
    }

    .history-panel {
        width: 100%;
        left: -100%;
        border-radius: 0;
    }

    .history-panel.visible {
        left: 0;
    }

    .history-stats {
        flex-direction: column;
        gap: 10px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .stat-label {
        margin-bottom: 0;
    }

    .game-item {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .game-actions {
        justify-content: center;
    }

    .replay-buttons {
        flex-wrap: wrap;
        gap: 6px;
    }

    .replay-btn {
        padding: 8px 10px;
    }
}
