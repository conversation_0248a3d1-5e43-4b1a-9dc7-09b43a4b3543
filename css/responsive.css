/* HuntGame - 响应式样式文件 */

/* 平板设备适配 (768px - 1024px) */
@media screen and (max-width: 1024px) {
    .top-bar {
        padding: 0 1rem;
    }
    
    .game-info {
        gap: 1rem;
    }
    
    .current-player {
        font-size: 1.1rem;
    }
    
    .game-status {
        font-size: 0.9rem;
    }
    
    .side-panel {
        width: 280px;
        right: -280px;
    }
    
    .panel-content {
        padding: 1.5rem;
    }
}

/* 手机设备适配 (最大768px) */
@media screen and (max-width: 768px) {
    /* 顶部状态栏移动端优化 */
    .top-bar {
        height: 50px;
        padding: 0 1rem;
        flex-wrap: wrap;
    }
    
    .game-info {
        flex: 1;
        min-width: 0;
    }
    
    .current-player {
        font-size: 1rem;
    }
    
    .game-status {
        font-size: 0.8rem;
        display: none; /* 在小屏幕上隐藏状态信息 */
    }
    
    .game-controls {
        gap: 0.5rem;
    }
    
    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        min-width: 60px;
    }
    
    /* 侧边面板移动端优化 */
    .side-panel {
        top: 50px;
        width: 100%;
        right: -100%;
        height: calc(100% - 50px);
    }
    
    .side-panel.active {
        right: 0;
    }
    
    .panel-content {
        padding: 1rem;
    }
    
    .panel-content h3 {
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }
    
    .setting-group {
        margin-bottom: 1rem;
    }
    
    /* 对话框移动端优化 */
    .dialog-content {
        padding: 1.5rem;
        margin: 1rem;
        width: calc(100% - 2rem);
    }
    
    .dialog-title {
        font-size: 1.2rem;
    }
    
    .dialog-actions {
        flex-direction: column;
    }
    
    .dialog-actions .btn {
        width: 100%;
    }
    
    /* 加载界面移动端优化 */
    .loading-content {
        padding: 1rem;
        max-width: 300px;
    }
    
    .loading-logo h1 {
        font-size: 2.5rem;
    }
    
    .loading-logo p {
        font-size: 1rem;
    }
}

/* 小屏手机适配 (最大480px) */
@media screen and (max-width: 480px) {
    .top-bar {
        height: 45px;
        padding: 0 0.5rem;
    }
    
    .current-player {
        font-size: 0.9rem;
    }
    
    .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.7rem;
        min-width: 50px;
    }
    
    .side-panel {
        top: 45px;
        height: calc(100% - 45px);
    }
    
    .panel-content {
        padding: 0.8rem;
    }
    
    .dialog-content {
        padding: 1rem;
    }
    
    .loading-logo h1 {
        font-size: 2rem;
    }
    
    .loading-logo p {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }
}

/* 横屏模式优化 */
@media screen and (orientation: landscape) and (max-height: 600px) {
    .top-bar {
        height: 40px;
    }
    
    .side-panel {
        top: 40px;
        height: calc(100% - 40px);
    }
    
    .panel-content {
        padding: 1rem;
    }
    
    .panel-content h3 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }
    
    .setting-group {
        margin-bottom: 0.8rem;
    }
    
    .panel-actions {
        margin-top: 1rem;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px; /* 确保触摸目标足够大 */
    }
    
    .form-select {
        min-height: 44px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    input[type="checkbox"] {
        transform: scale(1.2);
        margin-right: 0.8rem;
    }
    
    /* 移除hover效果，使用active状态 */
    .btn:hover {
        transform: none;
    }
    
    .btn:active {
        transform: scale(0.95);
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .loading-logo h1 {
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }
    
    .btn {
        border-width: 0.5px;
    }
    
    .dialog-content,
    .side-panel {
        border-width: 0.5px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    /* 游戏本身就是深色主题，这里可以做一些微调 */
    .loading-screen {
        background: linear-gradient(135deg, #1A0F0A 0%, #0F0805 100%);
    }
    
    .game-container {
        background: linear-gradient(135deg, #1A0F0A 0%, #0F0805 100%);
    }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .progress-fill {
        transition: none;
    }
    
    .side-panel {
        transition: none;
    }
    
    .btn {
        transition: none;
    }
}

/* 打印样式 */
@media print {
    .loading-screen,
    .game-hud,
    .side-panel,
    .dialog-overlay {
        display: none !important;
    }
    
    .game-container {
        background: white;
    }
    
    body {
        background: white;
        color: black;
    }
}
