/* HuntGame - 动画效果样式文件 */

/* 关键帧动画定义 */

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 淡出动画 */
@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* 从上滑入 */
@keyframes slideInFromTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 从右滑入 */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 从下滑入 */
@keyframes slideInFromBottom {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 缩放进入 */
@keyframes scaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* 弹跳动画 */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 摇摆动画 */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

/* 旋转动画 */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 闪烁动画 */
@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* 进度条填充动画 */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: 100%;
    }
}

/* 动画类应用 */

/* 加载界面动画 */
.loading-screen {
    animation: fadeIn 0.5s ease-out;
}

.loading-logo h1 {
    animation: slideInFromTop 0.8s ease-out 0.2s both;
}

.loading-logo p {
    animation: slideInFromTop 0.8s ease-out 0.4s both;
}

.loading-progress {
    animation: slideInFromBottom 0.8s ease-out 0.6s both;
}

.loading-text {
    animation: pulse 2s ease-in-out infinite;
}

/* 游戏界面动画 */
.game-container {
    animation: fadeIn 0.5s ease-out;
}

.top-bar {
    animation: slideInFromTop 0.6s ease-out 0.2s both;
}

/* 按钮动画 */
.btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn:hover {
    animation: none; /* 防止与hover效果冲突 */
}

.btn:active {
    animation: none;
    transform: scale(0.95);
}

.btn.animate-bounce {
    animation: bounce 0.6s ease-out;
}

.btn.animate-pulse {
    animation: pulse 1s ease-in-out infinite;
}

/* 侧边面板动画 */
.side-panel {
    transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.side-panel.animate-slide-in {
    animation: slideInFromRight 0.4s ease-out;
}

/* 对话框动画 */
.dialog-overlay {
    animation: fadeIn 0.3s ease-out;
}

.dialog-content {
    animation: scaleIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.dialog-overlay.animate-fade-out {
    animation: fadeOut 0.3s ease-out;
}

/* 工具提示动画 */
.tooltip {
    animation: fadeIn 0.2s ease-out;
    transition: opacity 0.2s ease-out;
}

.tooltip.animate-fade-out {
    animation: fadeOut 0.2s ease-out;
}

/* 游戏状态动画 */
.current-player {
    transition: color 0.3s ease;
}

.current-player.red-turn {
    color: #DC143C;
    text-shadow: 0 0 10px rgba(220, 20, 60, 0.5);
}

.current-player.black-turn {
    color: #2F4F4F;
    text-shadow: 0 0 10px rgba(47, 79, 79, 0.5);
}

.game-status.animate-blink {
    animation: blink 1s ease-in-out infinite;
}

/* 错误和成功状态动画 */
.error-shake {
    animation: shake 0.5s ease-in-out;
}

.success-bounce {
    animation: bounce 0.6s ease-out;
}

/* 加载旋转动画 */
.loading-spinner {
    animation: rotate 1s linear infinite;
}

/* 进度条动画 */
.progress-fill.animate-fill {
    animation: progressFill 2s ease-out;
}

/* 悬停效果增强 */
.btn-primary:hover {
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
    transform: translateY(-2px);
}

.btn-secondary:hover {
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
    transform: translateY(-1px);
}

/* 焦点动画 */
.form-select:focus,
input[type="checkbox"]:focus {
    animation: pulse 0.3s ease-out;
}

/* 页面切换动画 */
.page-transition-enter {
    animation: slideInFromRight 0.5s ease-out;
}

.page-transition-exit {
    animation: slideInFromRight 0.5s ease-out reverse;
}

/* 3D效果增强 */
.btn,
.dialog-content,
.side-panel {
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

/* 性能优化 */
.animate-gpu {
    transform: translateZ(0);
    will-change: transform, opacity;
}

/* 动画暂停类 */
.animations-paused * {
    animation-play-state: paused !important;
    transition: none !important;
}

/* 媒体查询 - 减少动画 */
@media (prefers-reduced-motion: reduce) {
    .loading-logo h1,
    .loading-logo p,
    .loading-progress,
    .top-bar {
        animation: none;
    }
    
    .btn:hover {
        transform: none;
    }
    
    .dialog-content {
        animation: fadeIn 0.1s ease-out;
    }
}

/* 高性能动画 */
@media (min-width: 1024px) {
    .btn:hover {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .side-panel {
        transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
}
