/**
 * HuntGame - 主入口文件
 * 游戏的主要初始化和控制逻辑
 */

class HuntGame {
    constructor() {
        // 游戏状态
        this.isInitialized = false;
        this.isLoading = true;
        this.loadingProgress = 0;
        
        // 核心组件
        this.sceneManager = null;
        this.gameEngine = null;
        this.aiEngine = null;
        this.inputManager = null;
        this.uiController = null;
        
        // DOM元素
        this.loadingScreen = null;
        this.gameContainer = null;
        this.progressFill = null;
        this.loadingText = null;
        
        // 初始化游戏
        this.init();
    }

    /**
     * 初始化游戏
     */
    async init() {
        try {
            console.log('HuntGame initializing...');
            
            // 获取DOM元素
            this.getDOMElements();
            
            // 显示加载界面
            this.showLoadingScreen();
            
            // 检查浏览器兼容性
            this.checkCompatibility();
            
            // 加载游戏资源和初始化组件
            await this.loadGame();
            
            // 隐藏加载界面，显示游戏
            this.hideLoadingScreen();
            
            console.log('HuntGame initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize HuntGame:', error);
            this.showError('游戏初始化失败: ' + error.message);
        }
    }

    /**
     * 获取DOM元素引用
     */
    getDOMElements() {
        this.loadingScreen = document.getElementById('loading-screen');
        this.gameContainer = document.getElementById('game-container');
        this.progressFill = document.getElementById('progress-fill');
        this.loadingText = document.getElementById('loading-text');
        
        if (!this.loadingScreen || !this.gameContainer) {
            throw new Error('Required DOM elements not found');
        }
    }

    /**
     * 检查浏览器兼容性
     */
    checkCompatibility() {
        // 检查WebGL支持
        if (!this.isWebGLSupported()) {
            throw new Error('您的浏览器不支持WebGL，无法运行3D游戏');
        }
        
        // 检查localStorage支持
        if (!StorageUtils.isSupported()) {
            console.warn('LocalStorage not supported, game progress will not be saved');
        }
        
        console.log('Browser compatibility check passed');
    }

    /**
     * 检查WebGL支持
     * @returns {boolean} 是否支持WebGL
     */
    isWebGLSupported() {
        try {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            return !!context;
        } catch (e) {
            return false;
        }
    }

    /**
     * 加载游戏
     */
    async loadGame() {
        const loadingSteps = [
            { name: '初始化3D场景...', action: () => this.initScene() },
            { name: '创建游戏引擎...', action: () => this.initGameEngine() },
            { name: '初始化AI系统...', action: () => this.initAI() },
            { name: '设置用户界面...', action: () => this.initUI() },
            { name: '加载游戏设置...', action: () => this.loadSettings() },
            { name: '准备游戏...', action: () => this.finalizeInit() }
        ];
        
        for (let i = 0; i < loadingSteps.length; i++) {
            const step = loadingSteps[i];
            
            this.updateLoadingProgress((i / loadingSteps.length) * 100, step.name);
            
            // 添加延迟以显示加载过程
            await this.delay(200);
            
            try {
                await step.action();
            } catch (error) {
                throw new Error(`${step.name} 失败: ${error.message}`);
            }
        }
        
        this.updateLoadingProgress(100, '加载完成！');
        await this.delay(500);
    }

    /**
     * 初始化3D场景
     */
    async initScene() {
        this.sceneManager = new SceneManager();

        if (!this.sceneManager.isInitialized) {
            throw new Error('Failed to initialize 3D scene');
        }

        console.log('3D Scene initialized successfully');
    }

    /**
     * 初始化游戏引擎
     */
    async initGameEngine() {
        this.gameEngine = new ChessEngine();
        console.log('Game engine initialized');
    }

    /**
     * 初始化AI系统
     */
    async initAI() {
        this.aiEngine = new ChessAI();

        // 初始化提示系统
        this.hintSystem = new HintSystem();
        this.hintPanel = new HintPanel();

        // 设置提示面板回调
        this.hintPanel.setCallbacks({
            onApplyHint: (move) => this.applyHint(move),
            onRefreshHint: () => this.refreshHint()
        });

        // 初始化游戏记录系统
        this.gameRecord = new GameRecord();
        this.gameReplay = null; // 将在场景初始化后创建
        this.historyPanel = new HistoryPanel();

        // 初始化音效和动画系统
        this.audioManager = new AudioManager();
        this.advancedAnimationManager = new AdvancedAnimationManager();
        this.visualEffectsManager = null; // 将在场景初始化后创建

        console.log('AI system and hint system initialized');
    }

    /**
     * 初始化用户界面
     */
    async initUI() {
        // 创建对话框管理器
        this.dialogManager = new DialogManager();

        // 创建HUD管理器
        this.hudManager = new HUDManager();

        // 设置HUD回调
        this.hudManager.setCallbacks({
            onHint: () => this.handleHint(),
            onUndo: () => this.handleUndo(),
            onNewGame: () => this.handleNewGame(),
            onSettingsChange: (settings) => this.handleSettingsChange(settings)
        });

        // 创建UI控制器
        this.uiController = {
            isInitialized: true,
            dialogManager: this.dialogManager,
            hudManager: this.hudManager,
            showDialog: (title, message) => this.dialogManager.alert(title, message),
            dispose: () => {
                if (this.dialogManager) this.dialogManager.dispose();
                if (this.hudManager) this.hudManager.dispose();
            }
        };

        console.log('UI system initialized');
    }

    /**
     * 设置基本事件监听器
     */
    setupBasicEventListeners() {
        // 新游戏按钮
        const newGameBtn = document.getElementById('btn-new-game');
        if (newGameBtn) {
            newGameBtn.addEventListener('click', () => {
                this.startNewGame();
            });
        }
        
        // 菜单按钮
        const menuBtn = document.getElementById('btn-menu');
        if (menuBtn) {
            menuBtn.addEventListener('click', () => {
                this.toggleMenu();
            });
        }
        
        // 关闭面板按钮
        const closePanelBtn = document.getElementById('btn-close-panel');
        if (closePanelBtn) {
            closePanelBtn.addEventListener('click', () => {
                this.closePanel();
            });
        }
    }

    /**
     * 加载游戏设置
     */
    async loadSettings() {
        const settings = StorageUtils.loadSettings();
        
        // 应用设置到UI
        const difficultySelect = document.getElementById('ai-difficulty');
        if (difficultySelect) {
            difficultySelect.value = settings.difficulty;
        }
        
        const enableHints = document.getElementById('enable-hints');
        if (enableHints) {
            enableHints.checked = settings.enableHints;
        }
        
        const enableSound = document.getElementById('enable-sound');
        if (enableSound) {
            enableSound.checked = settings.enableSound;
        }
        
        console.log('Game settings loaded');
    }

    /**
     * 完成初始化
     */
    async finalizeInit() {
        this.isInitialized = true;
        this.isLoading = false;

        // 创建复盘系统（需要渲染器）
        if (this.sceneManager && this.sceneManager.boardRenderer && this.sceneManager.piecesRenderer) {
            this.gameReplay = new GameReplay(
                this.gameEngine,
                this.sceneManager.boardRenderer,
                this.sceneManager.piecesRenderer
            );

            // 设置历史面板的复盘系统
            this.historyPanel.setGameReplay(this.gameReplay);
        }

        // 创建视觉特效管理器（需要场景和渲染器）
        if (this.sceneManager && this.sceneManager.scene && this.sceneManager.renderer) {
            this.visualEffectsManager = new VisualEffectsManager(
                this.sceneManager.scene,
                this.sceneManager.renderer
            );
        }

        // 启动渲染循环
        if (this.sceneManager && this.sceneManager.startRendering) {
            this.sceneManager.startRendering();
        }

        console.log('Game initialization finalized');
    }

    /**
     * 显示加载界面
     */
    showLoadingScreen() {
        if (this.loadingScreen) {
            this.loadingScreen.style.display = 'flex';
        }
        
        if (this.gameContainer) {
            this.gameContainer.style.display = 'none';
        }
    }

    /**
     * 隐藏加载界面
     */
    hideLoadingScreen() {
        if (this.loadingScreen) {
            this.loadingScreen.style.display = 'none';
        }
        
        if (this.gameContainer) {
            this.gameContainer.style.display = 'block';
        }
    }

    /**
     * 更新加载进度
     * @param {number} progress 进度百分比 (0-100)
     * @param {string} text 加载文本
     */
    updateLoadingProgress(progress, text) {
        this.loadingProgress = progress;
        
        if (this.progressFill) {
            this.progressFill.style.width = `${progress}%`;
        }
        
        if (this.loadingText) {
            this.loadingText.textContent = text;
        }
    }

    /**
     * 显示错误信息
     * @param {string} message 错误消息
     */
    showError(message) {
        if (this.loadingText) {
            this.loadingText.textContent = message;
            this.loadingText.style.color = '#ff6b6b';
        }
        
        console.error(message);
    }

    /**
     * 开始新游戏
     */
    async startNewGame() {
        console.log('Starting new game...');

        // 如果游戏正在进行，询问确认
        if (this.gameEngine && this.gameEngine.moveCount > 0) {
            const confirmed = await this.dialogManager.showNewGameConfirm();
            if (!confirmed) {
                return;
            }
        }

        // 结束当前游戏记录
        if (this.gameRecord && this.gameRecord.isCurrentlyRecording()) {
            const currentRecord = this.gameRecord.endRecording({
                result: 'incomplete',
                reason: 'new_game_started'
            });

            if (currentRecord) {
                await this.gameRecord.saveCurrentRecord();
            }
        }

        if (this.gameEngine && this.gameEngine.newGame) {
            this.gameEngine.newGame();
        }

        // 开始新的游戏记录
        if (this.gameRecord) {
            this.gameRecord.startRecording({
                mode: 'human_vs_ai',
                difficulty: this.aiEngine?.getDifficulty() || 'medium',
                playerRed: '玩家',
                playerBlack: 'AI'
            });

            // 记录初始棋盘
            const initialBoard = this.gameEngine.getBoard();
            this.gameRecord.recordInitialBoard(initialBoard);
        }

        // 更新HUD显示
        if (this.hudManager && this.gameEngine) {
            this.hudManager.updateGameState(this.gameEngine.getGameInfo());
        }
    }

    /**
     * 处理提示请求
     */
    async handleHint() {
        console.log('Hint requested');

        if (!this.hintSystem || !this.hintPanel) {
            this.hudManager.showToast('提示系统未初始化');
            return;
        }

        // 显示提示面板并开始分析
        this.hintPanel.show();

        try {
            // 获取当前游戏状态
            const board = this.gameEngine.getBoard();
            const currentPlayer = this.gameEngine.getCurrentPlayer();
            const gameInfo = this.gameEngine.getGameInfo();

            // 获取AI分析
            const hintData = await this.hintSystem.getHint(board, currentPlayer, gameInfo);

            if (hintData) {
                this.hintPanel.displayHint(hintData);
            } else {
                this.hintPanel.showError('无法生成提示');
            }

        } catch (error) {
            console.error('Error getting hint:', error);
            this.hintPanel.showError('提示生成失败');
        }
    }

    /**
     * 应用提示走法
     * @param {Object} move 走法对象
     */
    applyHint(move) {
        if (!move || !this.pieceController) {
            console.warn('Invalid hint move or piece controller not available');
            return;
        }

        // 应用建议的走法
        this.pieceController.attemptMove(move.to.row, move.to.col);
        this.hudManager.showToast('已应用AI建议');
    }

    /**
     * 刷新提示
     */
    async refreshHint() {
        console.log('Refreshing hint...');
        await this.handleHint();
    }

    /**
     * 处理历史面板请求
     */
    handleHistory() {
        console.log('History panel requested');

        if (this.historyPanel) {
            if (this.historyPanel.isVisible) {
                this.historyPanel.hide();
            } else {
                this.historyPanel.show();
            }
        } else {
            console.warn('History panel not initialized');
        }
    }

    /**
     * 记录走法
     * @param {Object} moveData 走法数据
     */
    recordMove(moveData) {
        if (this.gameRecord && this.gameRecord.isCurrentlyRecording()) {
            // 添加额外信息
            const enhancedMoveData = {
                ...moveData,
                timestamp: new Date().toISOString(),
                boardAfter: this.gameEngine.getBoard(),
                thinkingTime: moveData.thinkingTime || 0
            };

            this.gameRecord.recordMove(enhancedMoveData);
        }
    }

    /**
     * 结束游戏记录
     * @param {Object} gameResult 游戏结果
     */
    async endGameRecord(gameResult) {
        if (this.gameRecord && this.gameRecord.isCurrentlyRecording()) {
            const record = this.gameRecord.endRecording(gameResult);

            if (record) {
                const saved = await this.gameRecord.saveCurrentRecord();
                if (saved) {
                    console.log('Game record saved successfully');
                } else {
                    console.warn('Failed to save game record');
                }
            }
        }
    }

    /**
     * 播放音效
     * @param {string} soundKey 音效键名
     * @param {Object} options 播放选项
     */
    playSound(soundKey, options = {}) {
        if (this.audioManager) {
            this.audioManager.playSFX(soundKey, options);
        }
    }

    /**
     * 播放背景音乐
     * @param {string} musicKey 音乐键名
     * @param {Object} options 播放选项
     */
    playMusic(musicKey, options = {}) {
        if (this.audioManager) {
            this.audioManager.playMusic(musicKey, options);
        }
    }

    /**
     * 播放棋子移动动画和音效
     * @param {Object} moveData 移动数据
     */
    async playMoveEffects(moveData) {
        // 播放移动音效
        if (moveData.isCapture) {
            this.playSound('capture');

            // 播放吃子特效
            if (this.visualEffectsManager && moveData.toPosition) {
                this.visualEffectsManager.playCaptureEffect(moveData.toPosition);
            }
        } else {
            this.playSound('move');
        }

        // 播放移动轨迹特效
        if (this.visualEffectsManager && moveData.fromPosition && moveData.toPosition) {
            this.visualEffectsManager.playMoveTrailEffect(moveData.fromPosition, moveData.toPosition);
        }

        // 播放棋子移动动画
        if (this.advancedAnimationManager && moveData.pieceGroup && moveData.fromPosition && moveData.toPosition) {
            await this.advancedAnimationManager.animatePieceMove(
                moveData.pieceGroup,
                moveData.fromPosition,
                moveData.toPosition,
                { arc: true, arcHeight: 0.3 }
            );
        }

        // 检查将军状态
        if (moveData.isCheck) {
            this.playSound('check');

            if (this.visualEffectsManager && moveData.kingPosition) {
                this.visualEffectsManager.playCheckEffect(moveData.kingPosition);
            }
        }

        // 检查将死状态
        if (moveData.isCheckmate) {
            this.playSound('checkmate');

            if (this.visualEffectsManager && moveData.kingPosition) {
                this.visualEffectsManager.playVictoryEffect(moveData.kingPosition);
            }
        }
    }

    /**
     * 播放UI音效
     * @param {string} action UI动作
     */
    playUISound(action) {
        const soundMap = {
            click: 'click',
            hover: 'hover',
            select: 'select',
            open: 'open',
            close: 'close',
            success: 'success',
            error: 'error',
            invalid: 'invalid',
            hint: 'hint',
            undo: 'undo'
        };

        const soundKey = soundMap[action];
        if (soundKey) {
            this.playSound(soundKey);
        }
    }

    /**
     * 设置音效音量
     * @param {number} volume 音量 (0-1)
     */
    setSFXVolume(volume) {
        if (this.audioManager) {
            this.audioManager.setSFXVolume(volume);
        }
    }

    /**
     * 设置音乐音量
     * @param {number} volume 音量 (0-1)
     */
    setMusicVolume(volume) {
        if (this.audioManager) {
            this.audioManager.setMusicVolume(volume);
        }
    }

    /**
     * 启用/禁用音效
     * @param {boolean} enabled 是否启用
     */
    setAudioEnabled(enabled) {
        if (this.audioManager) {
            this.audioManager.setEnabled(enabled);
        }
    }

    /**
     * 处理悔棋请求
     */
    handleUndo() {
        console.log('Undo requested');

        if (this.gameEngine && this.gameEngine.undo) {
            const result = this.gameEngine.undo();
            if (result.success) {
                this.hudManager.showToast('已悔棋');
                this.hudManager.updateGameState(this.gameEngine.getGameInfo());
            } else {
                this.hudManager.showToast(result.message || '无法悔棋');
            }
        } else {
            this.hudManager.showToast('悔棋功能暂未实现');
        }
    }

    /**
     * 处理新游戏请求
     */
    async handleNewGame() {
        await this.startNewGame();
    }

    /**
     * 处理设置变化
     * @param {Object} settings 新设置
     */
    handleSettingsChange(settings) {
        console.log('Settings changed:', settings);

        // 应用AI难度设置
        if (this.aiEngine && this.aiEngine.setDifficulty) {
            this.aiEngine.setDifficulty(settings.difficulty);
        }

        // 应用其他设置...

        this.hudManager.showToast('设置已保存');
    }

    /**
     * 切换菜单面板
     */
    toggleMenu() {
        if (this.hudManager) {
            this.hudManager.togglePanel();
        }
    }

    /**
     * 关闭侧边面板
     */
    closePanel() {
        if (this.hudManager) {
            this.hudManager.closePanel();
        }
    }

    /**
     * 延迟函数
     * @param {number} ms 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 销毁游戏
     */
    dispose() {
        console.log('Disposing HuntGame...');
        
        // 销毁各个组件
        if (this.sceneManager && this.sceneManager.dispose) {
            this.sceneManager.dispose();
        }
        
        if (this.gameEngine && this.gameEngine.dispose) {
            this.gameEngine.dispose();
        }
        
        if (this.aiEngine && this.aiEngine.dispose) {
            this.aiEngine.dispose();
        }

        if (this.hintSystem) {
            this.hintSystem = null;
        }

        if (this.hintPanel && this.hintPanel.dispose) {
            this.hintPanel.dispose();
        }

        if (this.gameReplay && this.gameReplay.dispose) {
            this.gameReplay.dispose();
        }

        if (this.historyPanel && this.historyPanel.dispose) {
            this.historyPanel.dispose();
        }

        if (this.audioManager && this.audioManager.dispose) {
            this.audioManager.dispose();
        }

        if (this.advancedAnimationManager && this.advancedAnimationManager.dispose) {
            this.advancedAnimationManager.dispose();
        }

        if (this.visualEffectsManager && this.visualEffectsManager.dispose) {
            this.visualEffectsManager.dispose();
        }

        // 结束当前游戏记录
        if (this.gameRecord && this.gameRecord.isCurrentlyRecording()) {
            const record = this.gameRecord.endRecording({
                result: 'incomplete',
                reason: 'game_closed'
            });

            if (record) {
                this.gameRecord.saveCurrentRecord();
            }
        }

        if (this.uiController && this.uiController.dispose) {
            this.uiController.dispose();
        }
        
        this.isInitialized = false;
        
        console.log('HuntGame disposed');
    }
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    window.huntGame = new HuntGame();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.huntGame) {
        window.huntGame.dispose();
    }
});
