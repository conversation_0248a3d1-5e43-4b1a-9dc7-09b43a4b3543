/**
 * HuntGame - 视觉特效管理器
 * 粒子效果、光效、后处理等视觉特效管理
 */

class VisualEffectsManager {
    constructor(scene, renderer) {
        this.scene = scene;
        this.renderer = renderer;
        this.effects = new Map();
        this.particleSystems = new Map();
        
        // 特效配置
        this.config = {
            capture: {
                particleCount: 50,
                duration: 1000,
                colors: [0xff4444, 0xff8844, 0xffaa44]
            },
            check: {
                particleCount: 30,
                duration: 800,
                colors: [0xffff44, 0xffaa44]
            },
            move: {
                particleCount: 20,
                duration: 500,
                colors: [0x44ff44, 0x44ffaa]
            },
            victory: {
                particleCount: 100,
                duration: 2000,
                colors: [0xffd700, 0xffaa00, 0xff8800]
            }
        };
        
        this.init();
    }

    /**
     * 初始化特效系统
     */
    init() {
        // 创建粒子材质
        this.createParticleMaterials();
        
        // 创建光效
        this.createLightEffects();
        
        console.log('Visual effects manager initialized');
    }

    /**
     * 创建粒子材质
     */
    createParticleMaterials() {
        // 创建粒子纹理
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const context = canvas.getContext('2d');
        
        // 绘制圆形粒子
        const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
        gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.5)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        context.fillStyle = gradient;
        context.fillRect(0, 0, 64, 64);
        
        this.particleTexture = new THREE.CanvasTexture(canvas);
        
        // 创建基础粒子材质
        this.particleMaterial = new THREE.PointsMaterial({
            map: this.particleTexture,
            transparent: true,
            blending: THREE.AdditiveBlending,
            size: 0.1,
            sizeAttenuation: true,
            vertexColors: true
        });
    }

    /**
     * 创建光效
     */
    createLightEffects() {
        // 创建动态光源
        this.dynamicLight = new THREE.PointLight(0xffffff, 0, 5);
        this.dynamicLight.visible = false;
        this.scene.add(this.dynamicLight);
        
        // 创建环境光效果
        this.ambientLightEffect = new THREE.AmbientLight(0x404040, 0.1);
        this.ambientLightEffect.visible = false;
        this.scene.add(this.ambientLightEffect);
    }

    /**
     * 播放吃子特效
     * @param {THREE.Vector3} position 位置
     * @param {Object} options 选项
     */
    playCaptureEffect(position, options = {}) {
        const config = { ...this.config.capture, ...options };
        const effectId = `capture_${Date.now()}_${Math.random()}`;
        
        // 创建粒子系统
        const particles = this.createParticleSystem(config.particleCount, config.colors);
        particles.position.copy(position);
        particles.position.y += 0.2;
        
        this.scene.add(particles);
        this.particleSystems.set(effectId, particles);
        
        // 创建爆炸动画
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = elapsed / config.duration;
            
            if (progress >= 1) {
                // 清理粒子系统
                this.scene.remove(particles);
                this.particleSystems.delete(effectId);
                return;
            }
            
            // 更新粒子位置
            const positions = particles.geometry.attributes.position.array;
            const velocities = particles.userData.velocities;
            
            for (let i = 0; i < positions.length; i += 3) {
                positions[i] += velocities[i] * 0.016; // x
                positions[i + 1] += velocities[i + 1] * 0.016; // y
                positions[i + 2] += velocities[i + 2] * 0.016; // z
                
                // 重力效果
                velocities[i + 1] -= 0.01;
            }
            
            particles.geometry.attributes.position.needsUpdate = true;
            
            // 透明度衰减
            particles.material.opacity = 1 - progress;
            
            requestAnimationFrame(animate);
        };
        
        requestAnimationFrame(animate);
        
        // 添加光效
        this.addLightFlash(position, 0xff4444, 300);
        
        return effectId;
    }

    /**
     * 播放将军特效
     * @param {THREE.Vector3} position 位置
     * @param {Object} options 选项
     */
    playCheckEffect(position, options = {}) {
        const config = { ...this.config.check, ...options };
        const effectId = `check_${Date.now()}_${Math.random()}`;
        
        // 创建警告光环
        const ringGeometry = new THREE.RingGeometry(0.5, 0.8, 32);
        const ringMaterial = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });
        
        const ring = new THREE.Mesh(ringGeometry, ringMaterial);
        ring.position.copy(position);
        ring.position.y += 0.01;
        ring.rotation.x = -Math.PI / 2;
        
        this.scene.add(ring);
        
        // 脉冲动画
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = elapsed / config.duration;
            
            if (progress >= 1) {
                this.scene.remove(ring);
                return;
            }
            
            // 缩放脉冲
            const scale = 1 + Math.sin(progress * Math.PI * 6) * 0.2;
            ring.scale.setScalar(scale);
            
            // 透明度变化
            ring.material.opacity = 0.8 * (1 - progress);
            
            requestAnimationFrame(animate);
        };
        
        requestAnimationFrame(animate);
        
        // 添加闪烁光效
        this.addLightFlash(position, 0xffff00, 200);
        
        return effectId;
    }

    /**
     * 播放移动轨迹特效
     * @param {THREE.Vector3} fromPos 起始位置
     * @param {THREE.Vector3} toPos 目标位置
     * @param {Object} options 选项
     */
    playMoveTrailEffect(fromPos, toPos, options = {}) {
        const config = { ...this.config.move, ...options };
        const effectId = `trail_${Date.now()}_${Math.random()}`;
        
        // 创建轨迹线
        const points = [];
        const segments = 20;
        
        for (let i = 0; i <= segments; i++) {
            const t = i / segments;
            const point = new THREE.Vector3().lerpVectors(fromPos, toPos, t);
            
            // 添加弧形轨迹
            const arcHeight = 0.3;
            point.y += Math.sin(t * Math.PI) * arcHeight;
            
            points.push(point);
        }
        
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({
            color: 0x44ff44,
            transparent: true,
            opacity: 0.8
        });
        
        const trail = new THREE.Line(geometry, material);
        this.scene.add(trail);
        
        // 淡出动画
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = elapsed / config.duration;
            
            if (progress >= 1) {
                this.scene.remove(trail);
                return;
            }
            
            trail.material.opacity = 0.8 * (1 - progress);
            requestAnimationFrame(animate);
        };
        
        requestAnimationFrame(animate);
        
        return effectId;
    }

    /**
     * 播放胜利特效
     * @param {THREE.Vector3} position 位置
     * @param {Object} options 选项
     */
    playVictoryEffect(position, options = {}) {
        const config = { ...this.config.victory, ...options };
        const effectId = `victory_${Date.now()}_${Math.random()}`;
        
        // 创建烟花效果
        const fireworks = [];
        const fireworkCount = 5;
        
        for (let i = 0; i < fireworkCount; i++) {
            const firework = this.createParticleSystem(config.particleCount / fireworkCount, config.colors);
            const offset = new THREE.Vector3(
                (Math.random() - 0.5) * 4,
                Math.random() * 2 + 1,
                (Math.random() - 0.5) * 4
            );
            
            firework.position.copy(position).add(offset);
            this.scene.add(firework);
            fireworks.push(firework);
        }
        
        // 烟花动画
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = elapsed / config.duration;
            
            if (progress >= 1) {
                // 清理烟花
                fireworks.forEach(firework => {
                    this.scene.remove(firework);
                });
                return;
            }
            
            // 更新每个烟花
            fireworks.forEach((firework, index) => {
                const positions = firework.geometry.attributes.position.array;
                const velocities = firework.userData.velocities;
                
                for (let i = 0; i < positions.length; i += 3) {
                    positions[i] += velocities[i] * 0.016;
                    positions[i + 1] += velocities[i + 1] * 0.016;
                    positions[i + 2] += velocities[i + 2] * 0.016;
                    
                    // 重力
                    velocities[i + 1] -= 0.005;
                }
                
                firework.geometry.attributes.position.needsUpdate = true;
                firework.material.opacity = 1 - progress;
            });
            
            requestAnimationFrame(animate);
        };
        
        requestAnimationFrame(animate);
        
        // 添加多彩光效
        this.addLightFlash(position, 0xffd700, 1000);
        
        return effectId;
    }

    /**
     * 创建粒子系统
     * @param {number} count 粒子数量
     * @param {Array} colors 颜色数组
     * @returns {THREE.Points} 粒子系统
     */
    createParticleSystem(count, colors) {
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(count * 3);
        const colorArray = new Float32Array(count * 3);
        const velocities = new Float32Array(count * 3);
        
        for (let i = 0; i < count; i++) {
            const i3 = i * 3;
            
            // 初始位置（中心点）
            positions[i3] = 0;
            positions[i3 + 1] = 0;
            positions[i3 + 2] = 0;
            
            // 随机速度
            const speed = 0.1 + Math.random() * 0.2;
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.random() * Math.PI;
            
            velocities[i3] = Math.sin(phi) * Math.cos(theta) * speed;
            velocities[i3 + 1] = Math.cos(phi) * speed;
            velocities[i3 + 2] = Math.sin(phi) * Math.sin(theta) * speed;
            
            // 随机颜色
            const color = new THREE.Color(colors[Math.floor(Math.random() * colors.length)]);
            colorArray[i3] = color.r;
            colorArray[i3 + 1] = color.g;
            colorArray[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colorArray, 3));
        
        const material = this.particleMaterial.clone();
        const particles = new THREE.Points(geometry, material);
        
        // 存储速度数据
        particles.userData.velocities = velocities;
        
        return particles;
    }

    /**
     * 添加光效闪烁
     * @param {THREE.Vector3} position 位置
     * @param {number} color 颜色
     * @param {number} duration 持续时间
     */
    addLightFlash(position, color, duration) {
        this.dynamicLight.position.copy(position);
        this.dynamicLight.color.setHex(color);
        this.dynamicLight.intensity = 2;
        this.dynamicLight.visible = true;
        
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = elapsed / duration;
            
            if (progress >= 1) {
                this.dynamicLight.visible = false;
                return;
            }
            
            this.dynamicLight.intensity = 2 * (1 - progress);
            requestAnimationFrame(animate);
        };
        
        requestAnimationFrame(animate);
    }

    /**
     * 清理特效
     * @param {string} effectId 特效ID
     */
    clearEffect(effectId) {
        if (this.effects.has(effectId)) {
            const effect = this.effects.get(effectId);
            if (effect.dispose) {
                effect.dispose();
            }
            this.effects.delete(effectId);
        }
        
        if (this.particleSystems.has(effectId)) {
            const particles = this.particleSystems.get(effectId);
            this.scene.remove(particles);
            this.particleSystems.delete(effectId);
        }
    }

    /**
     * 清理所有特效
     */
    clearAllEffects() {
        this.effects.clear();
        
        this.particleSystems.forEach(particles => {
            this.scene.remove(particles);
        });
        this.particleSystems.clear();
    }

    /**
     * 销毁特效管理器
     */
    dispose() {
        this.clearAllEffects();
        
        if (this.dynamicLight) {
            this.scene.remove(this.dynamicLight);
        }
        
        if (this.ambientLightEffect) {
            this.scene.remove(this.ambientLightEffect);
        }
        
        console.log('Visual effects manager disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VisualEffectsManager;
} else {
    window.VisualEffectsManager = VisualEffectsManager;
}
