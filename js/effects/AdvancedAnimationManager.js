/**
 * HuntGame - 高级动画管理器
 * 完整的游戏动画和视觉效果管理系统
 */

class AdvancedAnimationManager {
    constructor() {
        this.animations = new Map();
        this.isRunning = false;
        this.animationId = null;
        
        // 动画配置
        this.config = {
            pieceMove: {
                duration: 500,
                easing: 'easeOutCubic'
            },
            pieceCapture: {
                duration: 300,
                easing: 'easeInBack'
            },
            pieceSelect: {
                duration: 200,
                easing: 'easeOutElastic'
            },
            boardHighlight: {
                duration: 300,
                easing: 'easeOutQuad'
            },
            uiTransition: {
                duration: 250,
                easing: 'easeOutCubic'
            }
        };
        
        // 缓动函数
        this.easingFunctions = {
            linear: t => t,
            easeInQuad: t => t * t,
            easeOutQuad: t => t * (2 - t),
            easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
            easeInCubic: t => t * t * t,
            easeOutCubic: t => (--t) * t * t + 1,
            easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
            easeInBack: t => t * t * (2.7 * t - 1.7),
            easeOutBack: t => 1 + (--t) * t * (2.7 * t + 1.7),
            easeOutElastic: t => {
                const c4 = (2 * Math.PI) / 3;
                return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
            },
            easeOutBounce: t => {
                const n1 = 7.5625;
                const d1 = 2.75;
                if (t < 1 / d1) {
                    return n1 * t * t;
                } else if (t < 2 / d1) {
                    return n1 * (t -= 1.5 / d1) * t + 0.75;
                } else if (t < 2.5 / d1) {
                    return n1 * (t -= 2.25 / d1) * t + 0.9375;
                } else {
                    return n1 * (t -= 2.625 / d1) * t + 0.984375;
                }
            }
        };
        
        this.startAnimationLoop();
        console.log('Advanced animation manager initialized');
    }

    /**
     * 开始动画循环
     */
    startAnimationLoop() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        
        const animate = (currentTime) => {
            if (!this.isRunning) return;
            
            this.update(currentTime);
            this.animationId = requestAnimationFrame(animate);
        };
        
        this.animationId = requestAnimationFrame(animate);
    }

    /**
     * 停止动画循环
     */
    stopAnimationLoop() {
        this.isRunning = false;
        
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    /**
     * 更新所有动画
     * @param {number} currentTime 当前时间
     */
    update(currentTime) {
        const completedAnimations = [];
        
        for (const [id, animation] of this.animations) {
            const elapsed = currentTime - animation.startTime;
            const progress = Math.min(elapsed / animation.duration, 1);
            
            // 应用缓动函数
            const easingFunc = this.easingFunctions[animation.easing] || this.easingFunctions.linear;
            const easedProgress = easingFunc(progress);
            
            // 执行动画更新
            if (animation.update) {
                animation.update(progress, easedProgress);
            }
            
            // 检查动画是否完成
            if (progress >= 1) {
                if (animation.loop) {
                    // 重新开始循环动画
                    animation.startTime = currentTime;
                } else {
                    // 标记为完成
                    completedAnimations.push(id);
                    
                    // 执行完成回调
                    if (animation.onComplete) {
                        animation.onComplete();
                    }
                }
            }
        }
        
        // 清理完成的动画
        completedAnimations.forEach(id => {
            this.animations.delete(id);
        });
    }

    /**
     * 播放棋子移动动画
     * @param {THREE.Group} pieceGroup 棋子组
     * @param {THREE.Vector3} fromPos 起始位置
     * @param {THREE.Vector3} toPos 目标位置
     * @param {Object} options 动画选项
     * @returns {Promise} 动画完成Promise
     */
    animatePieceMove(pieceGroup, fromPos, toPos, options = {}) {
        return new Promise((resolve) => {
            const animationId = `piece_move_${Date.now()}_${Math.random()}`;
            const config = { ...this.config.pieceMove, ...options };
            
            const animation = {
                id: animationId,
                type: 'pieceMove',
                target: pieceGroup,
                startTime: performance.now(),
                duration: config.duration,
                easing: config.easing,
                fromPos: fromPos.clone(),
                toPos: toPos.clone(),
                onComplete: resolve,
                update: (progress, easedProgress) => {
                    // 位置插值
                    pieceGroup.position.lerpVectors(fromPos, toPos, easedProgress);
                    
                    // 添加弧形轨迹
                    if (config.arc) {
                        const arcHeight = config.arcHeight || 0.5;
                        const arcProgress = Math.sin(easedProgress * Math.PI);
                        pieceGroup.position.y += arcProgress * arcHeight;
                    }
                    
                    // 添加旋转效果
                    if (config.rotate) {
                        pieceGroup.rotation.y = easedProgress * Math.PI * 2;
                    }
                }
            };
            
            this.animations.set(animationId, animation);
        });
    }

    /**
     * 播放棋子被吃动画
     * @param {THREE.Group} pieceGroup 棋子组
     * @param {Object} options 动画选项
     * @returns {Promise} 动画完成Promise
     */
    animatePieceCapture(pieceGroup, options = {}) {
        return new Promise((resolve) => {
            const animationId = `piece_capture_${Date.now()}_${Math.random()}`;
            const config = { ...this.config.pieceCapture, ...options };
            
            const initialScale = pieceGroup.scale.clone();
            const initialRotation = pieceGroup.rotation.clone();
            
            const animation = {
                id: animationId,
                type: 'pieceCapture',
                target: pieceGroup,
                startTime: performance.now(),
                duration: config.duration,
                easing: config.easing,
                onComplete: () => {
                    // 隐藏棋子
                    pieceGroup.visible = false;
                    resolve();
                },
                update: (progress, easedProgress) => {
                    // 缩放效果
                    const scale = 1 - easedProgress;
                    pieceGroup.scale.setScalar(scale);
                    
                    // 旋转效果
                    pieceGroup.rotation.y = initialRotation.y + easedProgress * Math.PI * 2;
                    
                    // 透明度效果
                    pieceGroup.traverse((child) => {
                        if (child.material) {
                            child.material.transparent = true;
                            child.material.opacity = 1 - easedProgress;
                        }
                    });
                }
            };
            
            this.animations.set(animationId, animation);
        });
    }

    /**
     * 播放棋子选中动画
     * @param {THREE.Group} pieceGroup 棋子组
     * @param {Object} options 动画选项
     * @returns {string} 动画ID
     */
    animatePieceSelect(pieceGroup, options = {}) {
        const animationId = `piece_select_${Date.now()}_${Math.random()}`;
        const config = { ...this.config.pieceSelect, ...options };
        
        const initialY = pieceGroup.position.y;
        const hoverHeight = config.hoverHeight || 0.2;
        
        const animation = {
            id: animationId,
            type: 'pieceSelect',
            target: pieceGroup,
            startTime: performance.now(),
            duration: config.duration,
            easing: config.easing,
            loop: true,
            update: (progress, easedProgress) => {
                // 悬浮效果
                const floatOffset = Math.sin(progress * Math.PI * 4) * 0.05;
                pieceGroup.position.y = initialY + hoverHeight + floatOffset;
                
                // 发光效果
                pieceGroup.traverse((child) => {
                    if (child.material && child.material.emissive) {
                        const intensity = 0.3 + Math.sin(progress * Math.PI * 6) * 0.1;
                        child.material.emissive.setScalar(intensity);
                    }
                });
            }
        };
        
        this.animations.set(animationId, animation);
        return animationId;
    }

    /**
     * 播放棋盘高亮动画
     * @param {THREE.Mesh} highlightMesh 高亮网格
     * @param {Object} options 动画选项
     * @returns {string} 动画ID
     */
    animateBoardHighlight(highlightMesh, options = {}) {
        const animationId = `board_highlight_${Date.now()}_${Math.random()}`;
        const config = { ...this.config.boardHighlight, ...options };
        
        const animation = {
            id: animationId,
            type: 'boardHighlight',
            target: highlightMesh,
            startTime: performance.now(),
            duration: config.duration,
            easing: config.easing,
            loop: true,
            update: (progress, easedProgress) => {
                // 透明度脉冲
                const opacity = 0.3 + Math.sin(progress * Math.PI * 4) * 0.2;
                highlightMesh.material.opacity = opacity;
                
                // 颜色变化
                if (config.colorPulse) {
                    const hue = (progress * 360) % 360;
                    highlightMesh.material.color.setHSL(hue / 360, 0.8, 0.6);
                }
            }
        };
        
        this.animations.set(animationId, animation);
        return animationId;
    }

    /**
     * 停止动画
     * @param {string} animationId 动画ID
     */
    stopAnimation(animationId) {
        if (this.animations.has(animationId)) {
            const animation = this.animations.get(animationId);
            
            // 执行完成回调
            if (animation.onComplete) {
                animation.onComplete();
            }
            
            this.animations.delete(animationId);
        }
    }

    /**
     * 停止所有动画
     */
    stopAllAnimations() {
        this.animations.clear();
    }

    /**
     * 获取动画状态
     * @returns {Object} 动画状态
     */
    getAnimationState() {
        return {
            isRunning: this.isRunning,
            activeAnimations: this.animations.size,
            animationTypes: Array.from(this.animations.values()).map(a => a.type)
        };
    }

    /**
     * 销毁动画管理器
     */
    dispose() {
        this.stopAnimationLoop();
        this.stopAllAnimations();
        
        console.log('Advanced animation manager disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedAnimationManager;
} else {
    window.AdvancedAnimationManager = AdvancedAnimationManager;
}
