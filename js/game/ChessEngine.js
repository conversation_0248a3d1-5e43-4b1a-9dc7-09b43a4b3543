/**
 * HuntGame - 象棋游戏引擎
 * 游戏引擎占位符，将在后续阶段完善
 */

class ChessEngine {
    constructor() {
        this.gameState = new GameState();
        this.history = new GameHistory();
        this.isInitialized = true;
        this.moveCount = 0;
    }

    /**
     * 开始新游戏
     */
    newGame() {
        this.gameState.initializeBoard();
        this.history.clear();
        this.moveCount = 0;
        console.log('New game started');
    }

    /**
     * 移动棋子
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {Object} 移动结果
     */
    movePiece(fromRow, fromCol, toRow, toCol) {
        const result = this.gameState.movePiece(fromRow, fromCol, toRow, toCol);
        
        if (result.success) {
            this.history.addMove(result.move);
            this.moveCount++;
        }
        
        return result;
    }

    /**
     * 悔棋
     * @returns {Object} 悔棋结果
     */
    undo() {
        const lastMove = this.history.undoLastMove();
        
        if (!lastMove) {
            return {
                success: false,
                message: '没有可以悔棋的步数'
            };
        }
        
        // 这里应该恢复棋盘状态，暂时简化处理
        this.moveCount = Math.max(0, this.moveCount - 1);
        
        return {
            success: true,
            message: '悔棋成功'
        };
    }

    /**
     * 获取游戏信息
     * @returns {Object} 游戏信息
     */
    getGameInfo() {
        return {
            ...this.gameState.getGameInfo(),
            moveCount: this.moveCount
        };
    }

    /**
     * 销毁引擎
     */
    dispose() {
        console.log('Chess engine disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChessEngine;
} else {
    window.ChessEngine = ChessEngine;
}
