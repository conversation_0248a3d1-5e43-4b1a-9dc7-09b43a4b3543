/**
 * HuntGame - 游戏状态管理器
 * 管理棋盘状态、游戏进程和状态变化
 */

class GameState {
    constructor() {
        // 棋盘状态 (10行9列)
        this.board = this.createEmptyBoard();
        
        // 游戏状态
        this.currentPlayer = PieceColor.RED; // 红方先行
        this.gameStatus = 'playing'; // 'playing', 'check', 'checkmate', 'stalemate', 'draw'
        this.winner = null;
        
        // 游戏统计
        this.moveCount = 0;
        this.capturedPieces = {
            [PieceColor.RED]: [],
            [PieceColor.BLACK]: []
        };
        
        // 游戏时间
        this.startTime = null;
        this.endTime = null;
        this.playerTime = {
            [PieceColor.RED]: 0,
            [PieceColor.BLACK]: 0
        };
        this.lastMoveTime = null;
        
        // 特殊状态
        this.isInCheck = false;
        this.checkingPieces = [];
        
        // 规则引擎
        this.rules = new ChessRules();
        
        // 初始化棋盘
        this.initializeBoard();
    }

    /**
     * 创建空棋盘
     * @returns {Array} 10x9的空棋盘数组
     */
    createEmptyBoard() {
        const board = [];
        for (let row = 0; row < 10; row++) {
            board[row] = [];
            for (let col = 0; col < 9; col++) {
                board[row][col] = null;
            }
        }
        return board;
    }

    /**
     * 初始化棋盘到开局状态
     */
    initializeBoard() {
        // 清空棋盘
        this.board = this.createEmptyBoard();
        
        // 放置黑方棋子 (上方)
        this.placePiece(0, 0, PieceType.CHARIOT, PieceColor.BLACK);
        this.placePiece(0, 1, PieceType.HORSE, PieceColor.BLACK);
        this.placePiece(0, 2, PieceType.ELEPHANT, PieceColor.BLACK);
        this.placePiece(0, 3, PieceType.ADVISOR, PieceColor.BLACK);
        this.placePiece(0, 4, PieceType.KING, PieceColor.BLACK);
        this.placePiece(0, 5, PieceType.ADVISOR, PieceColor.BLACK);
        this.placePiece(0, 6, PieceType.ELEPHANT, PieceColor.BLACK);
        this.placePiece(0, 7, PieceType.HORSE, PieceColor.BLACK);
        this.placePiece(0, 8, PieceType.CHARIOT, PieceColor.BLACK);
        
        this.placePiece(2, 1, PieceType.CANNON, PieceColor.BLACK);
        this.placePiece(2, 7, PieceType.CANNON, PieceColor.BLACK);
        
        this.placePiece(3, 0, PieceType.PAWN, PieceColor.BLACK);
        this.placePiece(3, 2, PieceType.PAWN, PieceColor.BLACK);
        this.placePiece(3, 4, PieceType.PAWN, PieceColor.BLACK);
        this.placePiece(3, 6, PieceType.PAWN, PieceColor.BLACK);
        this.placePiece(3, 8, PieceType.PAWN, PieceColor.BLACK);
        
        // 放置红方棋子 (下方)
        this.placePiece(9, 0, PieceType.CHARIOT, PieceColor.RED);
        this.placePiece(9, 1, PieceType.HORSE, PieceColor.RED);
        this.placePiece(9, 2, PieceType.ELEPHANT, PieceColor.RED);
        this.placePiece(9, 3, PieceType.ADVISOR, PieceColor.RED);
        this.placePiece(9, 4, PieceType.KING, PieceColor.RED);
        this.placePiece(9, 5, PieceType.ADVISOR, PieceColor.RED);
        this.placePiece(9, 6, PieceType.ELEPHANT, PieceColor.RED);
        this.placePiece(9, 7, PieceType.HORSE, PieceColor.RED);
        this.placePiece(9, 8, PieceType.CHARIOT, PieceColor.RED);
        
        this.placePiece(7, 1, PieceType.CANNON, PieceColor.RED);
        this.placePiece(7, 7, PieceType.CANNON, PieceColor.RED);
        
        this.placePiece(6, 0, PieceType.PAWN, PieceColor.RED);
        this.placePiece(6, 2, PieceType.PAWN, PieceColor.RED);
        this.placePiece(6, 4, PieceType.PAWN, PieceColor.RED);
        this.placePiece(6, 6, PieceType.PAWN, PieceColor.RED);
        this.placePiece(6, 8, PieceType.PAWN, PieceColor.RED);
        
        // 重置游戏状态
        this.currentPlayer = PieceColor.RED;
        this.gameStatus = 'playing';
        this.winner = null;
        this.moveCount = 0;
        this.capturedPieces = {
            [PieceColor.RED]: [],
            [PieceColor.BLACK]: []
        };
        this.startTime = Date.now();
        this.lastMoveTime = this.startTime;
        this.isInCheck = false;
        this.checkingPieces = [];
        
        console.log('Game board initialized');
    }

    /**
     * 在指定位置放置棋子
     * @param {number} row 行坐标
     * @param {number} col 列坐标
     * @param {string} type 棋子类型
     * @param {string} color 棋子颜色
     */
    placePiece(row, col, type, color) {
        if (!this.rules.isInBoard(row, col)) {
            console.warn(`Invalid position: ${row}, ${col}`);
            return;
        }
        
        const piece = {
            type: type,
            color: color,
            id: `${color}_${type}_${row}_${col}_${Date.now()}`,
            moveCount: 0,
            position: { row, col }
        };
        
        this.board[row][col] = piece;
    }

    /**
     * 获取指定位置的棋子
     * @param {number} row 行坐标
     * @param {number} col 列坐标
     * @returns {Object|null} 棋子对象或null
     */
    getPiece(row, col) {
        if (!this.rules.isInBoard(row, col)) {
            return null;
        }
        return this.board[row][col];
    }

    /**
     * 移动棋子
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {Object} 移动结果
     */
    movePiece(fromRow, fromCol, toRow, toCol) {
        // 验证走法是否合法
        if (!this.rules.isValidMove(this.board, fromRow, fromCol, toRow, toCol)) {
            return {
                success: false,
                error: 'Invalid move',
                message: '不合法的走法'
            };
        }
        
        const piece = this.board[fromRow][fromCol];
        
        // 检查是否轮到该玩家
        if (piece.color !== this.currentPlayer) {
            return {
                success: false,
                error: 'Wrong player',
                message: '不是该玩家的回合'
            };
        }
        
        // 记录被吃的棋子
        const capturedPiece = this.board[toRow][toCol];
        
        // 执行移动
        this.board[toRow][toCol] = piece;
        this.board[fromRow][fromCol] = null;
        
        // 更新棋子信息
        piece.position = { row: toRow, col: toCol };
        piece.moveCount++;
        
        // 处理被吃的棋子
        if (capturedPiece) {
            this.capturedPieces[this.currentPlayer].push(capturedPiece);
        }
        
        // 更新游戏状态
        this.moveCount++;
        this.lastMoveTime = Date.now();
        
        // 检查游戏状态
        this.updateGameStatus();
        
        // 切换玩家
        if (this.gameStatus === 'playing' || this.gameStatus === 'check') {
            this.switchPlayer();
        }
        
        return {
            success: true,
            move: {
                from: { row: fromRow, col: fromCol },
                to: { row: toRow, col: toCol },
                piece: piece,
                capturedPiece: capturedPiece,
                moveCount: this.moveCount,
                timestamp: this.lastMoveTime
            },
            gameStatus: this.gameStatus,
            isInCheck: this.isInCheck
        };
    }

    /**
     * 切换当前玩家
     */
    switchPlayer() {
        this.currentPlayer = this.currentPlayer === PieceColor.RED ? PieceColor.BLACK : PieceColor.RED;
    }

    /**
     * 更新游戏状态
     */
    updateGameStatus() {
        // 检查是否将军
        this.checkForCheck();

        // 检查是否将死或困毙
        if (this.isInCheck) {
            if (this.isCheckmate()) {
                this.gameStatus = 'checkmate';
                this.winner = this.currentPlayer; // 当前玩家获胜（因为对方被将死）
                this.endTime = Date.now();
            } else {
                this.gameStatus = 'check';
            }
        } else if (this.isStalemate()) {
            this.gameStatus = 'stalemate';
            this.endTime = Date.now();
        } else {
            this.gameStatus = 'playing';
        }
    }

    /**
     * 检查是否将军
     */
    checkForCheck() {
        const opponentColor = this.currentPlayer === PieceColor.RED ? PieceColor.BLACK : PieceColor.RED;
        const kingPosition = this.findKing(opponentColor);

        if (!kingPosition) {
            return; // 找不到王，游戏异常
        }

        this.isInCheck = false;
        this.checkingPieces = [];

        // 检查是否有己方棋子能攻击对方的王
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = this.board[row][col];
                if (piece && piece.color === this.currentPlayer) {
                    if (this.rules.isValidMove(this.board, row, col, kingPosition.row, kingPosition.col)) {
                        this.isInCheck = true;
                        this.checkingPieces.push({ row, col, piece });
                    }
                }
            }
        }
    }

    /**
     * 查找指定颜色的王
     * @param {string} color 棋子颜色
     * @returns {Object|null} 王的位置
     */
    findKing(color) {
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = this.board[row][col];
                if (piece && piece.type === PieceType.KING && piece.color === color) {
                    return { row, col };
                }
            }
        }
        return null;
    }

    /**
     * 检查是否将死
     * @returns {boolean} 是否将死
     */
    isCheckmate() {
        if (!this.isInCheck) {
            return false;
        }

        const opponentColor = this.currentPlayer === PieceColor.RED ? PieceColor.BLACK : PieceColor.RED;

        // 检查对方是否有任何合法走法可以解除将军
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = this.board[row][col];
                if (piece && piece.color === opponentColor) {
                    const possibleMoves = this.rules.getPossibleMoves(this.board, row, col);

                    for (const move of possibleMoves) {
                        // 模拟走法
                        if (this.simulateMove(move.fromRow, move.fromCol, move.toRow, move.toCol)) {
                            return false; // 找到解除将军的走法
                        }
                    }
                }
            }
        }

        return true; // 没有找到解除将军的走法，将死
    }

    /**
     * 检查是否困毙
     * @returns {boolean} 是否困毙
     */
    isStalemate() {
        const opponentColor = this.currentPlayer === PieceColor.RED ? PieceColor.BLACK : PieceColor.RED;

        // 检查对方是否有任何合法走法
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = this.board[row][col];
                if (piece && piece.color === opponentColor) {
                    const possibleMoves = this.rules.getPossibleMoves(this.board, row, col);

                    for (const move of possibleMoves) {
                        // 模拟走法，检查是否会导致自己被将军
                        if (this.simulateMove(move.fromRow, move.fromCol, move.toRow, move.toCol)) {
                            return false; // 找到合法走法
                        }
                    }
                }
            }
        }

        return true; // 没有合法走法，困毙
    }

    /**
     * 模拟走法，检查是否合法（不会导致自己被将军）
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否安全
     */
    simulateMove(fromRow, fromCol, toRow, toCol) {
        // 保存原始状态
        const originalPiece = this.board[fromRow][fromCol];
        const originalTarget = this.board[toRow][toCol];

        // 执行模拟移动
        this.board[toRow][toCol] = originalPiece;
        this.board[fromRow][fromCol] = null;

        // 检查移动后是否被将军
        const isSafe = !this.wouldBeInCheck(originalPiece.color);

        // 恢复原始状态
        this.board[fromRow][fromCol] = originalPiece;
        this.board[toRow][toCol] = originalTarget;

        return isSafe;
    }

    /**
     * 检查指定颜色是否会被将军
     * @param {string} color 棋子颜色
     * @returns {boolean} 是否会被将军
     */
    wouldBeInCheck(color) {
        const kingPosition = this.findKing(color);
        if (!kingPosition) {
            return false;
        }

        const opponentColor = color === PieceColor.RED ? PieceColor.BLACK : PieceColor.RED;

        // 检查对方是否有棋子能攻击王
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = this.board[row][col];
                if (piece && piece.color === opponentColor) {
                    if (this.rules.isValidMove(this.board, row, col, kingPosition.row, kingPosition.col)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 获取当前玩家的所有合法走法
     * @returns {Array} 合法走法列表
     */
    getAllValidMoves() {
        const moves = [];

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = this.board[row][col];
                if (piece && piece.color === this.currentPlayer) {
                    const pieceMoves = this.rules.getPossibleMoves(this.board, row, col);

                    // 过滤掉会导致自己被将军的走法
                    const validMoves = pieceMoves.filter(move =>
                        this.simulateMove(move.fromRow, move.fromCol, move.toRow, move.toCol)
                    );

                    moves.push(...validMoves);
                }
            }
        }

        return moves;
    }

    /**
     * 获取游戏状态信息
     * @returns {Object} 游戏状态信息
     */
    getGameInfo() {
        return {
            currentPlayer: this.currentPlayer,
            gameStatus: this.gameStatus,
            winner: this.winner,
            moveCount: this.moveCount,
            isInCheck: this.isInCheck,
            checkingPieces: this.checkingPieces,
            capturedPieces: this.capturedPieces,
            startTime: this.startTime,
            endTime: this.endTime,
            gameTime: this.endTime ? this.endTime - this.startTime : Date.now() - this.startTime
        };
    }

    /**
     * 复制棋盘状态
     * @returns {Array} 棋盘副本
     */
    cloneBoard() {
        const clonedBoard = [];
        for (let row = 0; row < 10; row++) {
            clonedBoard[row] = [];
            for (let col = 0; col < 9; col++) {
                const piece = this.board[row][col];
                if (piece) {
                    clonedBoard[row][col] = { ...piece };
                } else {
                    clonedBoard[row][col] = null;
                }
            }
        }
        return clonedBoard;
    }

    /**
     * 从JSON数据恢复游戏状态
     * @param {Object} data 游戏状态数据
     */
    fromJSON(data) {
        this.board = data.board || this.createEmptyBoard();
        this.currentPlayer = data.currentPlayer || PieceColor.RED;
        this.gameStatus = data.gameStatus || 'playing';
        this.winner = data.winner || null;
        this.moveCount = data.moveCount || 0;
        this.capturedPieces = data.capturedPieces || {
            [PieceColor.RED]: [],
            [PieceColor.BLACK]: []
        };
        this.startTime = data.startTime || Date.now();
        this.endTime = data.endTime || null;
        this.isInCheck = data.isInCheck || false;
        this.checkingPieces = data.checkingPieces || [];

        // 重新检查游戏状态
        this.updateGameStatus();
    }

    /**
     * 转换为JSON数据
     * @returns {Object} 游戏状态数据
     */
    toJSON() {
        return {
            board: this.board,
            currentPlayer: this.currentPlayer,
            gameStatus: this.gameStatus,
            winner: this.winner,
            moveCount: this.moveCount,
            capturedPieces: this.capturedPieces,
            startTime: this.startTime,
            endTime: this.endTime,
            isInCheck: this.isInCheck,
            checkingPieces: this.checkingPieces
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameState;
} else {
    window.GameState = GameState;
}
