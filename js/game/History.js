/**
 * HuntGame - 游戏历史记录管理器
 * 管理游戏的历史记录和悔棋功能
 */

class GameHistory {
    constructor() {
        this.moves = [];
        this.maxHistorySize = GameConfig.game.maxHistoryCount || 100;
    }

    /**
     * 添加移动记录
     * @param {Object} move 移动记录
     */
    addMove(move) {
        this.moves.push(move);
        
        // 限制历史记录大小
        if (this.moves.length > this.maxHistorySize) {
            this.moves.shift();
        }
    }

    /**
     * 获取最后一步移动
     * @returns {Object|null} 最后一步移动
     */
    getLastMove() {
        return this.moves.length > 0 ? this.moves[this.moves.length - 1] : null;
    }

    /**
     * 撤销最后一步移动
     * @returns {Object|null} 被撤销的移动
     */
    undoLastMove() {
        return this.moves.pop() || null;
    }

    /**
     * 清空历史记录
     */
    clear() {
        this.moves = [];
    }

    /**
     * 获取移动数量
     * @returns {number} 移动数量
     */
    getMoveCount() {
        return this.moves.length;
    }

    /**
     * 转换为JSON
     * @returns {Object} JSON数据
     */
    toJSON() {
        return {
            moves: this.moves,
            maxHistorySize: this.maxHistorySize
        };
    }

    /**
     * 从JSON恢复
     * @param {Object} data JSON数据
     */
    fromJSON(data) {
        this.moves = data.moves || [];
        this.maxHistorySize = data.maxHistorySize || 100;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameHistory;
} else {
    window.GameHistory = GameHistory;
}
