/**
 * HuntGame - 游戏复盘系统
 * 完整的游戏复盘、回放和分析功能
 */

class GameReplay {
    constructor(gameEngine, boardRenderer, piecesRenderer) {
        this.gameEngine = gameEngine;
        this.boardRenderer = boardRenderer;
        this.piecesRenderer = piecesRenderer;
        
        // 复盘状态
        this.isReplaying = false;
        this.currentRecord = null;
        this.currentMoveIndex = -1;
        this.replaySpeed = 1000; // 毫秒
        this.autoPlay = false;
        this.autoPlayTimer = null;
        
        // 复盘模式
        this.replayMode = 'step'; // 'step', 'auto', 'analysis'
        
        // 回调函数
        this.callbacks = {
            onMoveChanged: null,
            onReplayStarted: null,
            onReplayEnded: null,
            onPositionChanged: null
        };
        
        console.log('Game replay system initialized');
    }

    /**
     * 开始复盘
     * @param {Object} gameRecord 游戏记录
     * @returns {boolean} 是否成功开始
     */
    startReplay(gameRecord) {
        if (!gameRecord || !gameRecord.moves || gameRecord.moves.length === 0) {
            console.warn('Invalid game record for replay');
            return false;
        }
        
        this.currentRecord = gameRecord;
        this.currentMoveIndex = -1;
        this.isReplaying = true;
        
        // 重置到初始位置
        this.resetToInitialPosition();
        
        // 触发回调
        if (this.callbacks.onReplayStarted) {
            this.callbacks.onReplayStarted(gameRecord);
        }
        
        console.log('Started replay for game:', gameRecord.id);
        return true;
    }

    /**
     * 结束复盘
     */
    endReplay() {
        if (!this.isReplaying) return;
        
        this.stopAutoPlay();
        this.isReplaying = false;
        this.currentRecord = null;
        this.currentMoveIndex = -1;
        
        // 触发回调
        if (this.callbacks.onReplayEnded) {
            this.callbacks.onReplayEnded();
        }
        
        console.log('Ended replay');
    }

    /**
     * 重置到初始位置
     */
    resetToInitialPosition() {
        if (!this.currentRecord) return;
        
        // 重置游戏引擎到初始状态
        this.gameEngine.reset();
        
        // 如果有初始棋盘状态，使用它
        if (this.currentRecord.initialBoard) {
            this.gameEngine.setBoardFromSerialized(this.currentRecord.initialBoard);
        }
        
        // 重新创建棋子渲染
        const currentBoard = this.gameEngine.getBoard();
        this.piecesRenderer.createPiecesFromBoard(currentBoard);
        
        this.currentMoveIndex = -1;
        this.updateReplayUI();
    }

    /**
     * 下一步
     * @returns {boolean} 是否成功
     */
    nextMove() {
        if (!this.isReplaying || !this.currentRecord) return false;
        
        if (this.currentMoveIndex >= this.currentRecord.moves.length - 1) {
            console.log('Already at the last move');
            return false;
        }
        
        this.currentMoveIndex++;
        return this.executeMove(this.currentMoveIndex);
    }

    /**
     * 上一步
     * @returns {boolean} 是否成功
     */
    previousMove() {
        if (!this.isReplaying || !this.currentRecord) return false;
        
        if (this.currentMoveIndex <= -1) {
            console.log('Already at the beginning');
            return false;
        }
        
        this.currentMoveIndex--;
        return this.restorePosition(this.currentMoveIndex);
    }

    /**
     * 跳转到指定步数
     * @param {number} moveIndex 步数索引
     * @returns {boolean} 是否成功
     */
    goToMove(moveIndex) {
        if (!this.isReplaying || !this.currentRecord) return false;
        
        if (moveIndex < -1 || moveIndex >= this.currentRecord.moves.length) {
            console.warn('Invalid move index:', moveIndex);
            return false;
        }
        
        this.currentMoveIndex = moveIndex;
        return this.restorePosition(moveIndex);
    }

    /**
     * 跳转到开始
     */
    goToStart() {
        this.goToMove(-1);
    }

    /**
     * 跳转到结束
     */
    goToEnd() {
        if (this.currentRecord && this.currentRecord.moves.length > 0) {
            this.goToMove(this.currentRecord.moves.length - 1);
        }
    }

    /**
     * 开始自动播放
     */
    startAutoPlay() {
        if (!this.isReplaying || this.autoPlay) return;
        
        this.autoPlay = true;
        this.scheduleNextMove();
        
        console.log('Started auto play');
    }

    /**
     * 停止自动播放
     */
    stopAutoPlay() {
        if (!this.autoPlay) return;
        
        this.autoPlay = false;
        
        if (this.autoPlayTimer) {
            clearTimeout(this.autoPlayTimer);
            this.autoPlayTimer = null;
        }
        
        console.log('Stopped auto play');
    }

    /**
     * 切换自动播放
     */
    toggleAutoPlay() {
        if (this.autoPlay) {
            this.stopAutoPlay();
        } else {
            this.startAutoPlay();
        }
    }

    /**
     * 设置播放速度
     * @param {number} speed 速度（毫秒）
     */
    setReplaySpeed(speed) {
        this.replaySpeed = Math.max(100, Math.min(speed, 5000));
        console.log('Replay speed set to:', this.replaySpeed);
    }

    /**
     * 执行指定步数的走法
     * @param {number} moveIndex 步数索引
     * @returns {boolean} 是否成功
     */
    async executeMove(moveIndex) {
        if (!this.currentRecord || moveIndex < 0 || moveIndex >= this.currentRecord.moves.length) {
            return false;
        }
        
        const move = this.currentRecord.moves[moveIndex];
        
        try {
            // 执行走法
            const result = this.gameEngine.movePiece(
                move.from.row, move.from.col,
                move.to.row, move.to.col
            );
            
            if (result.success) {
                // 更新3D渲染
                await this.piecesRenderer.movePiece(
                    move.from.row, move.from.col,
                    move.to.row, move.to.col,
                    true // 动画
                );
                
                // 更新UI
                this.updateReplayUI();
                
                // 触发回调
                if (this.callbacks.onMoveChanged) {
                    this.callbacks.onMoveChanged(move, moveIndex);
                }
                
                if (this.callbacks.onPositionChanged) {
                    this.callbacks.onPositionChanged(this.gameEngine.getBoard(), moveIndex);
                }
                
                return true;
            }
            
        } catch (error) {
            console.error('Error executing replay move:', error);
        }
        
        return false;
    }

    /**
     * 恢复到指定位置
     * @param {number} moveIndex 步数索引
     * @returns {boolean} 是否成功
     */
    restorePosition(moveIndex) {
        if (!this.currentRecord) return false;
        
        // 重置到初始状态
        this.resetToInitialPosition();
        
        // 如果是开始位置，直接返回
        if (moveIndex < 0) {
            return true;
        }
        
        // 重新执行到指定位置的所有走法
        for (let i = 0; i <= moveIndex; i++) {
            const move = this.currentRecord.moves[i];
            
            const result = this.gameEngine.movePiece(
                move.from.row, move.from.col,
                move.to.row, move.to.col
            );
            
            if (!result.success) {
                console.error('Failed to restore position at move:', i);
                return false;
            }
        }
        
        // 重新创建棋子渲染（无动画）
        const currentBoard = this.gameEngine.getBoard();
        this.piecesRenderer.createPiecesFromBoard(currentBoard);
        
        // 更新UI
        this.updateReplayUI();
        
        // 触发回调
        if (this.callbacks.onPositionChanged) {
            this.callbacks.onPositionChanged(currentBoard, moveIndex);
        }
        
        return true;
    }

    /**
     * 安排下一步移动
     */
    scheduleNextMove() {
        if (!this.autoPlay || !this.isReplaying) return;
        
        this.autoPlayTimer = setTimeout(() => {
            if (this.autoPlay && this.isReplaying) {
                const success = this.nextMove();
                
                if (success) {
                    this.scheduleNextMove();
                } else {
                    // 到达结尾，停止自动播放
                    this.stopAutoPlay();
                }
            }
        }, this.replaySpeed);
    }

    /**
     * 更新复盘UI
     */
    updateReplayUI() {
        // 这里可以更新复盘控制面板的显示
        // 比如当前步数、进度条等
    }

    /**
     * 获取当前走法信息
     * @returns {Object|null} 当前走法
     */
    getCurrentMove() {
        if (!this.currentRecord || this.currentMoveIndex < 0) {
            return null;
        }
        
        return this.currentRecord.moves[this.currentMoveIndex] || null;
    }

    /**
     * 获取复盘进度
     * @returns {Object} 进度信息
     */
    getReplayProgress() {
        if (!this.currentRecord) {
            return { current: 0, total: 0, percentage: 0 };
        }
        
        const current = this.currentMoveIndex + 1;
        const total = this.currentRecord.moves.length;
        const percentage = total > 0 ? (current / total) * 100 : 0;
        
        return { current, total, percentage };
    }

    /**
     * 获取走法列表
     * @returns {Array} 走法列表
     */
    getMoveList() {
        return this.currentRecord ? this.currentRecord.moves : [];
    }

    /**
     * 分析当前位置
     * @returns {Object|null} 分析结果
     */
    analyzeCurrentPosition() {
        if (!this.isReplaying) return null;
        
        const currentBoard = this.gameEngine.getBoard();
        const currentPlayer = this.gameEngine.getCurrentPlayer();
        
        // 这里可以集成AI分析
        // 返回位置评估、最佳走法等信息
        
        return {
            board: currentBoard,
            player: currentPlayer,
            moveIndex: this.currentMoveIndex,
            evaluation: null, // 可以添加AI评估
            bestMoves: null   // 可以添加最佳走法建议
        };
    }

    /**
     * 导出当前位置为FEN
     * @returns {string|null} FEN字符串
     */
    exportCurrentPositionFEN() {
        if (!this.isReplaying) return null;
        
        const currentBoard = this.gameEngine.getBoard();
        // 这里需要实现FEN导出功能
        return this.boardToFEN(currentBoard);
    }

    /**
     * 设置回调函数
     * @param {Object} callbacks 回调函数对象
     */
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * 检查是否正在复盘
     * @returns {boolean} 是否正在复盘
     */
    isCurrentlyReplaying() {
        return this.isReplaying;
    }

    /**
     * 检查是否在自动播放
     * @returns {boolean} 是否在自动播放
     */
    isAutoPlaying() {
        return this.autoPlay;
    }

    /**
     * 获取复盘状态
     * @returns {Object} 复盘状态
     */
    getReplayState() {
        return {
            isReplaying: this.isReplaying,
            autoPlay: this.autoPlay,
            currentMoveIndex: this.currentMoveIndex,
            replaySpeed: this.replaySpeed,
            progress: this.getReplayProgress(),
            currentMove: this.getCurrentMove()
        };
    }

    /**
     * 棋盘转FEN格式（简化版）
     * @param {Array} board 棋盘状态
     * @returns {string} FEN字符串
     */
    boardToFEN(board) {
        // 这里应该实现完整的FEN转换
        // 暂时返回简化版本
        return 'fen_placeholder';
    }

    /**
     * 销毁复盘系统
     */
    dispose() {
        this.endReplay();
        this.callbacks = {};
        
        console.log('Game replay system disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameReplay;
} else {
    window.GameReplay = GameReplay;
}
