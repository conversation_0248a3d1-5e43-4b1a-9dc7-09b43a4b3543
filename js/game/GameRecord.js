/**
 * HuntGame - 游戏记录系统
 * 完整的游戏记录、保存、加载和复盘功能
 */

class GameRecord {
    constructor() {
        this.currentRecord = null;
        this.isRecording = false;
        this.maxRecords = 100; // 最大保存记录数
        
        // 记录格式版本
        this.version = '1.0';
        
        console.log('Game record system initialized');
    }

    /**
     * 开始记录新游戏
     * @param {Object} gameInfo 游戏信息
     */
    startRecording(gameInfo = {}) {
        this.currentRecord = {
            id: this.generateRecordId(),
            version: this.version,
            startTime: new Date().toISOString(),
            endTime: null,
            
            // 游戏信息
            gameInfo: {
                mode: gameInfo.mode || 'human_vs_ai',
                difficulty: gameInfo.difficulty || 'medium',
                playerRed: gameInfo.playerRed || '玩家',
                playerBlack: gameInfo.playerBlack || 'AI',
                timeControl: gameInfo.timeControl || null
            },
            
            // 游戏状态
            initialBoard: null,
            moves: [],
            positions: [],
            
            // 游戏结果
            result: null,
            winner: null,
            endReason: null,
            
            // 统计信息
            stats: {
                totalMoves: 0,
                capturedPieces: { red: [], black: [] },
                thinkingTime: { red: 0, black: 0 },
                checksGiven: { red: 0, black: 0 }
            },
            
            // 元数据
            metadata: {
                created: new Date().toISOString(),
                tags: [],
                notes: ''
            }
        };
        
        this.isRecording = true;
        console.log('Started recording game:', this.currentRecord.id);
    }

    /**
     * 记录初始棋盘状态
     * @param {Array} board 棋盘状态
     */
    recordInitialBoard(board) {
        if (!this.isRecording || !this.currentRecord) return;
        
        this.currentRecord.initialBoard = this.serializeBoard(board);
        this.currentRecord.positions.push({
            moveNumber: 0,
            board: this.serializeBoard(board),
            fen: this.boardToFEN(board),
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 记录走法
     * @param {Object} moveData 走法数据
     */
    recordMove(moveData) {
        if (!this.isRecording || !this.currentRecord) return;
        
        const move = {
            moveNumber: this.currentRecord.moves.length + 1,
            player: moveData.player,
            from: { row: moveData.from.row, col: moveData.from.col },
            to: { row: moveData.to.row, col: moveData.to.col },
            piece: moveData.piece,
            capturedPiece: moveData.capturedPiece || null,
            isCheck: moveData.isCheck || false,
            isCheckmate: moveData.isCheckmate || false,
            notation: this.generateNotation(moveData),
            timestamp: new Date().toISOString(),
            thinkingTime: moveData.thinkingTime || 0,
            evaluation: moveData.evaluation || null
        };
        
        this.currentRecord.moves.push(move);
        this.currentRecord.stats.totalMoves++;
        
        // 记录被吃棋子
        if (move.capturedPiece) {
            const opponentColor = move.player === 'red' ? 'black' : 'red';
            this.currentRecord.stats.capturedPieces[opponentColor].push({
                piece: move.capturedPiece,
                moveNumber: move.moveNumber,
                position: { row: move.to.row, col: move.to.col }
            });
        }
        
        // 记录将军
        if (move.isCheck) {
            this.currentRecord.stats.checksGiven[move.player]++;
        }
        
        // 记录思考时间
        this.currentRecord.stats.thinkingTime[move.player] += move.thinkingTime;
        
        // 记录位置
        if (moveData.boardAfter) {
            this.currentRecord.positions.push({
                moveNumber: move.moveNumber,
                board: this.serializeBoard(moveData.boardAfter),
                fen: this.boardToFEN(moveData.boardAfter),
                timestamp: move.timestamp
            });
        }
        
        console.log(`Recorded move ${move.moveNumber}: ${move.notation}`);
    }

    /**
     * 结束记录
     * @param {Object} gameResult 游戏结果
     */
    endRecording(gameResult = {}) {
        if (!this.isRecording || !this.currentRecord) return;
        
        this.currentRecord.endTime = new Date().toISOString();
        this.currentRecord.result = gameResult.result || 'unknown';
        this.currentRecord.winner = gameResult.winner || null;
        this.currentRecord.endReason = gameResult.reason || 'unknown';
        
        // 计算游戏时长
        const startTime = new Date(this.currentRecord.startTime);
        const endTime = new Date(this.currentRecord.endTime);
        this.currentRecord.duration = endTime - startTime;
        
        this.isRecording = false;
        
        console.log('Ended recording game:', this.currentRecord.id);
        return this.currentRecord;
    }

    /**
     * 保存当前记录
     * @returns {boolean} 是否保存成功
     */
    async saveCurrentRecord() {
        if (!this.currentRecord) {
            console.warn('No current record to save');
            return false;
        }
        
        try {
            const records = this.loadAllRecords();
            
            // 检查是否已存在
            const existingIndex = records.findIndex(r => r.id === this.currentRecord.id);
            
            if (existingIndex >= 0) {
                records[existingIndex] = this.currentRecord;
            } else {
                records.unshift(this.currentRecord); // 添加到开头
                
                // 限制记录数量
                if (records.length > this.maxRecords) {
                    records.splice(this.maxRecords);
                }
            }
            
            // 保存到本地存储
            localStorage.setItem('huntgame_records', JSON.stringify(records));
            
            console.log('Game record saved:', this.currentRecord.id);
            return true;
            
        } catch (error) {
            console.error('Failed to save game record:', error);
            return false;
        }
    }

    /**
     * 加载所有记录
     * @returns {Array} 游戏记录数组
     */
    loadAllRecords() {
        try {
            const data = localStorage.getItem('huntgame_records');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Failed to load game records:', error);
            return [];
        }
    }

    /**
     * 根据ID加载记录
     * @param {string} recordId 记录ID
     * @returns {Object|null} 游戏记录
     */
    loadRecord(recordId) {
        const records = this.loadAllRecords();
        return records.find(r => r.id === recordId) || null;
    }

    /**
     * 删除记录
     * @param {string} recordId 记录ID
     * @returns {boolean} 是否删除成功
     */
    deleteRecord(recordId) {
        try {
            const records = this.loadAllRecords();
            const filteredRecords = records.filter(r => r.id !== recordId);
            
            localStorage.setItem('huntgame_records', JSON.stringify(filteredRecords));
            
            console.log('Game record deleted:', recordId);
            return true;
            
        } catch (error) {
            console.error('Failed to delete game record:', error);
            return false;
        }
    }

    /**
     * 清空所有记录
     * @returns {boolean} 是否清空成功
     */
    clearAllRecords() {
        try {
            localStorage.removeItem('huntgame_records');
            console.log('All game records cleared');
            return true;
        } catch (error) {
            console.error('Failed to clear game records:', error);
            return false;
        }
    }

    /**
     * 导出记录为PGN格式
     * @param {string} recordId 记录ID
     * @returns {string|null} PGN字符串
     */
    exportToPGN(recordId) {
        const record = this.loadRecord(recordId);
        if (!record) return null;
        
        let pgn = '';
        
        // PGN头部信息
        pgn += `[Event "HuntGame"]\n`;
        pgn += `[Site "Local"]\n`;
        pgn += `[Date "${record.startTime.split('T')[0]}"]\n`;
        pgn += `[Round "1"]\n`;
        pgn += `[Red "${record.gameInfo.playerRed}"]\n`;
        pgn += `[Black "${record.gameInfo.playerBlack}"]\n`;
        pgn += `[Result "${this.getResultNotation(record.result, record.winner)}"]\n`;
        pgn += `[Mode "${record.gameInfo.mode}"]\n`;
        pgn += `[Difficulty "${record.gameInfo.difficulty}"]\n`;
        pgn += `\n`;
        
        // 走法
        for (let i = 0; i < record.moves.length; i++) {
            const move = record.moves[i];
            
            if (i % 2 === 0) {
                pgn += `${Math.floor(i / 2) + 1}. `;
            }
            
            pgn += `${move.notation} `;
            
            if ((i + 1) % 10 === 0) {
                pgn += '\n';
            }
        }
        
        pgn += ` ${this.getResultNotation(record.result, record.winner)}`;
        
        return pgn;
    }

    /**
     * 从PGN导入记录
     * @param {string} pgnString PGN字符串
     * @returns {Object|null} 游戏记录
     */
    importFromPGN(pgnString) {
        try {
            // 简化的PGN解析（实际实现会更复杂）
            const lines = pgnString.split('\n');
            const headers = {};
            const moves = [];
            
            // 解析头部
            for (const line of lines) {
                const headerMatch = line.match(/\[(\w+)\s+"([^"]+)"\]/);
                if (headerMatch) {
                    headers[headerMatch[1]] = headerMatch[2];
                }
            }
            
            // 解析走法（简化版）
            const moveLines = lines.filter(line => !line.startsWith('[') && line.trim());
            const moveText = moveLines.join(' ');
            const moveMatches = moveText.match(/\d+\.\s*([^\s]+)\s*([^\s]*)/g);
            
            if (moveMatches) {
                for (const match of moveMatches) {
                    const parts = match.split(/\s+/);
                    if (parts.length >= 2) {
                        moves.push(parts[1]);
                        if (parts[2] && !parts[2].includes('.')) {
                            moves.push(parts[2]);
                        }
                    }
                }
            }
            
            // 创建记录对象
            const record = {
                id: this.generateRecordId(),
                version: this.version,
                startTime: headers.Date ? new Date(headers.Date).toISOString() : new Date().toISOString(),
                endTime: null,
                gameInfo: {
                    mode: headers.Mode || 'unknown',
                    difficulty: headers.Difficulty || 'medium',
                    playerRed: headers.Red || '红方',
                    playerBlack: headers.Black || '黑方'
                },
                moves: [], // 需要进一步解析
                result: headers.Result || 'unknown',
                metadata: {
                    created: new Date().toISOString(),
                    imported: true,
                    source: 'pgn'
                }
            };
            
            return record;
            
        } catch (error) {
            console.error('Failed to import PGN:', error);
            return null;
        }
    }

    /**
     * 生成记录ID
     * @returns {string} 唯一ID
     */
    generateRecordId() {
        return 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 序列化棋盘
     * @param {Array} board 棋盘状态
     * @returns {Array} 序列化的棋盘
     */
    serializeBoard(board) {
        return board.map(row => 
            row.map(piece => 
                piece ? { type: piece.type, color: piece.color } : null
            )
        );
    }

    /**
     * 棋盘转FEN格式
     * @param {Array} board 棋盘状态
     * @returns {string} FEN字符串
     */
    boardToFEN(board) {
        // 简化的FEN实现（中国象棋FEN格式）
        let fen = '';
        
        for (let row = 0; row < 10; row++) {
            let emptyCount = 0;
            
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                
                if (piece) {
                    if (emptyCount > 0) {
                        fen += emptyCount;
                        emptyCount = 0;
                    }
                    fen += this.pieceToFENChar(piece);
                } else {
                    emptyCount++;
                }
            }
            
            if (emptyCount > 0) {
                fen += emptyCount;
            }
            
            if (row < 9) {
                fen += '/';
            }
        }
        
        return fen;
    }

    /**
     * 棋子转FEN字符
     * @param {Object} piece 棋子对象
     * @returns {string} FEN字符
     */
    pieceToFENChar(piece) {
        const chars = {
            'general': piece.color === 'red' ? 'K' : 'k',
            'advisor': piece.color === 'red' ? 'A' : 'a',
            'elephant': piece.color === 'red' ? 'B' : 'b',
            'horse': piece.color === 'red' ? 'N' : 'n',
            'chariot': piece.color === 'red' ? 'R' : 'r',
            'cannon': piece.color === 'red' ? 'C' : 'c',
            'soldier': piece.color === 'red' ? 'P' : 'p'
        };
        return chars[piece.type] || '?';
    }

    /**
     * 生成走法记号
     * @param {Object} moveData 走法数据
     * @returns {string} 走法记号
     */
    generateNotation(moveData) {
        const pieceChars = {
            'general': '将',
            'advisor': '士',
            'elephant': '象',
            'horse': '马',
            'chariot': '车',
            'cannon': '炮',
            'soldier': '兵'
        };
        
        const piece = pieceChars[moveData.piece.type] || moveData.piece.type;
        const from = `${moveData.from.col + 1}${10 - moveData.from.row}`;
        const to = `${moveData.to.col + 1}${10 - moveData.to.row}`;
        
        let notation = `${piece}${from}`;
        
        if (moveData.capturedPiece) {
            notation += '×';
        } else {
            notation += '-';
        }
        
        notation += to;
        
        if (moveData.isCheckmate) {
            notation += '#';
        } else if (moveData.isCheck) {
            notation += '+';
        }
        
        return notation;
    }

    /**
     * 获取结果记号
     * @param {string} result 结果
     * @param {string} winner 获胜者
     * @returns {string} 结果记号
     */
    getResultNotation(result, winner) {
        if (result === 'checkmate') {
            return winner === 'red' ? '1-0' : '0-1';
        } else if (result === 'draw' || result === 'stalemate') {
            return '1/2-1/2';
        }
        return '*';
    }

    /**
     * 获取当前记录
     * @returns {Object|null} 当前记录
     */
    getCurrentRecord() {
        return this.currentRecord;
    }

    /**
     * 检查是否正在记录
     * @returns {boolean} 是否正在记录
     */
    isCurrentlyRecording() {
        return this.isRecording;
    }

    /**
     * 获取记录统计
     * @returns {Object} 统计信息
     */
    getRecordStats() {
        const records = this.loadAllRecords();
        
        return {
            totalGames: records.length,
            wins: records.filter(r => r.winner === 'red').length,
            losses: records.filter(r => r.winner === 'black').length,
            draws: records.filter(r => r.result === 'draw').length,
            averageGameLength: records.length > 0 ? 
                records.reduce((sum, r) => sum + (r.stats?.totalMoves || 0), 0) / records.length : 0
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameRecord;
} else {
    window.GameRecord = GameRecord;
}
