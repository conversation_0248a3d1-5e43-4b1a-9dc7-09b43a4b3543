/**
 * HuntGame - 中国象棋规则引擎
 * 实现完整的中国象棋规则验证和走棋逻辑
 */

// 棋子类型常量
const PieceType = {
    KING: 'king',       // 将/帅
    ADVISOR: 'advisor', // 士
    ELEPHANT: 'elephant', // 象
    HORSE: 'horse',     // 马
    CHARIOT: 'chariot', // 车
    CANNON: 'cannon',   // 炮
    PAWN: 'pawn'        // 兵/卒
};

// 棋子颜色常量
const PieceColor = {
    RED: 'red',    // 红方
    BLACK: 'black' // 黑方
};

// 棋子定义
const PieceDefinitions = {
    [PieceColor.RED]: {
        [PieceType.KING]: { name: '帅', symbol: '帥' },
        [PieceType.ADVISOR]: { name: '仕', symbol: '仕' },
        [PieceType.ELEPHANT]: { name: '相', symbol: '相' },
        [PieceType.HORSE]: { name: '马', symbol: '馬' },
        [PieceType.CHARIOT]: { name: '车', symbol: '車' },
        [PieceType.CANNON]: { name: '炮', symbol: '炮' },
        [PieceType.PAWN]: { name: '兵', symbol: '兵' }
    },
    [PieceColor.BLACK]: {
        [PieceType.KING]: { name: '将', symbol: '將' },
        [PieceType.ADVISOR]: { name: '士', symbol: '士' },
        [PieceType.ELEPHANT]: { name: '象', symbol: '象' },
        [PieceType.HORSE]: { name: '马', symbol: '馬' },
        [PieceType.CHARIOT]: { name: '车', symbol: '車' },
        [PieceType.CANNON]: { name: '炮', symbol: '砲' },
        [PieceType.PAWN]: { name: '卒', symbol: '卒' }
    }
};

class ChessRules {
    constructor() {
        // 棋盘尺寸
        this.BOARD_WIDTH = 9;
        this.BOARD_HEIGHT = 10;
        
        // 河界
        this.RIVER_ROW = 4.5;
        
        // 九宫格范围
        this.PALACE_COLS = [3, 4, 5];
        this.RED_PALACE_ROWS = [0, 1, 2];
        this.BLACK_PALACE_ROWS = [7, 8, 9];
    }

    /**
     * 检查坐标是否在棋盘范围内
     * @param {number} row 行坐标 (0-9)
     * @param {number} col 列坐标 (0-8)
     * @returns {boolean} 是否在范围内
     */
    isInBoard(row, col) {
        return row >= 0 && row < this.BOARD_HEIGHT && col >= 0 && col < this.BOARD_WIDTH;
    }

    /**
     * 检查坐标是否在九宫格内
     * @param {number} row 行坐标
     * @param {number} col 列坐标
     * @param {string} color 棋子颜色
     * @returns {boolean} 是否在九宫格内
     */
    isInPalace(row, col, color) {
        if (!this.PALACE_COLS.includes(col)) {
            return false;
        }
        
        if (color === PieceColor.RED) {
            return this.RED_PALACE_ROWS.includes(row);
        } else {
            return this.BLACK_PALACE_ROWS.includes(row);
        }
    }

    /**
     * 检查坐标是否在己方半场
     * @param {number} row 行坐标
     * @param {string} color 棋子颜色
     * @returns {boolean} 是否在己方半场
     */
    isInOwnSide(row, color) {
        if (color === PieceColor.RED) {
            return row <= this.RIVER_ROW;
        } else {
            return row >= this.RIVER_ROW;
        }
    }

    /**
     * 检查路径是否被阻挡
     * @param {Array} board 棋盘状态
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 路径是否被阻挡
     */
    isPathBlocked(board, fromRow, fromCol, toRow, toCol) {
        const rowDiff = toRow - fromRow;
        const colDiff = toCol - fromCol;
        
        // 计算移动方向
        const rowStep = rowDiff === 0 ? 0 : (rowDiff > 0 ? 1 : -1);
        const colStep = colDiff === 0 ? 0 : (colDiff > 0 ? 1 : -1);
        
        let currentRow = fromRow + rowStep;
        let currentCol = fromCol + colStep;
        
        // 检查路径上的每个位置
        while (currentRow !== toRow || currentCol !== toCol) {
            if (board[currentRow][currentCol] !== null) {
                return true; // 路径被阻挡
            }
            currentRow += rowStep;
            currentCol += colStep;
        }
        
        return false; // 路径畅通
    }

    /**
     * 计算两点之间的棋子数量（用于炮的走法）
     * @param {Array} board 棋盘状态
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {number} 中间棋子数量
     */
    countPiecesBetween(board, fromRow, fromCol, toRow, toCol) {
        const rowDiff = toRow - fromRow;
        const colDiff = toCol - fromCol;
        
        // 只能在直线上计算
        if (rowDiff !== 0 && colDiff !== 0) {
            return -1;
        }
        
        const rowStep = rowDiff === 0 ? 0 : (rowDiff > 0 ? 1 : -1);
        const colStep = colDiff === 0 ? 0 : (colDiff > 0 ? 1 : -1);
        
        let count = 0;
        let currentRow = fromRow + rowStep;
        let currentCol = fromCol + colStep;
        
        while (currentRow !== toRow || currentCol !== toCol) {
            if (board[currentRow][currentCol] !== null) {
                count++;
            }
            currentRow += rowStep;
            currentCol += colStep;
        }
        
        return count;
    }

    /**
     * 验证将/帅的走法
     * @param {Array} board 棋盘状态
     * @param {Object} piece 棋子对象
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否合法
     */
    validateKingMove(board, piece, fromRow, fromCol, toRow, toCol) {
        // 只能在九宫格内移动
        if (!this.isInPalace(toRow, toCol, piece.color)) {
            return false;
        }
        
        // 只能走一格，且只能横走或竖走
        const rowDiff = Math.abs(toRow - fromRow);
        const colDiff = Math.abs(toCol - fromCol);
        
        if ((rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)) {
            return true;
        }
        
        // 特殊情况：将帅对面（飞将）
        if (fromCol === toCol && this.isKingFaceToFace(board, fromRow, fromCol, toRow, toCol)) {
            return true;
        }
        
        return false;
    }

    /**
     * 检查将帅是否对面
     * @param {Array} board 棋盘状态
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 是否为飞将
     */
    isKingFaceToFace(board, fromRow, fromCol, toRow, toCol) {
        // 必须在同一列
        if (fromCol !== toCol) {
            return false;
        }
        
        // 检查目标位置是否有对方的将/帅
        const targetPiece = board[toRow][toCol];
        if (!targetPiece || targetPiece.type !== PieceType.KING) {
            return false;
        }
        
        // 检查中间是否没有其他棋子
        return this.countPiecesBetween(board, fromRow, fromCol, toRow, toCol) === 0;
    }

    /**
     * 验证士的走法
     * @param {Array} board 棋盘状态
     * @param {Object} piece 棋子对象
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否合法
     */
    validateAdvisorMove(board, piece, fromRow, fromCol, toRow, toCol) {
        // 只能在九宫格内移动
        if (!this.isInPalace(toRow, toCol, piece.color)) {
            return false;
        }
        
        // 只能斜走一格
        const rowDiff = Math.abs(toRow - fromRow);
        const colDiff = Math.abs(toCol - fromCol);
        
        return rowDiff === 1 && colDiff === 1;
    }

    /**
     * 验证象的走法
     * @param {Array} board 棋盘状态
     * @param {Object} piece 棋子对象
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否合法
     */
    validateElephantMove(board, piece, fromRow, fromCol, toRow, toCol) {
        // 不能过河
        if (!this.isInOwnSide(toRow, piece.color)) {
            return false;
        }
        
        // 只能斜走两格（田字格）
        const rowDiff = toRow - fromRow;
        const colDiff = toCol - fromCol;
        
        if (Math.abs(rowDiff) !== 2 || Math.abs(colDiff) !== 2) {
            return false;
        }
        
        // 检查象眼是否被堵
        const eyeRow = fromRow + rowDiff / 2;
        const eyeCol = fromCol + colDiff / 2;
        
        return board[eyeRow][eyeCol] === null;
    }

    /**
     * 验证马的走法
     * @param {Array} board 棋盘状态
     * @param {Object} piece 棋子对象
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否合法
     */
    validateHorseMove(board, piece, fromRow, fromCol, toRow, toCol) {
        const rowDiff = Math.abs(toRow - fromRow);
        const colDiff = Math.abs(toCol - fromCol);
        
        // 马走日字
        if (!((rowDiff === 2 && colDiff === 1) || (rowDiff === 1 && colDiff === 2))) {
            return false;
        }
        
        // 检查马腿是否被堵
        let legRow, legCol;
        
        if (rowDiff === 2) {
            // 竖走两格，横走一格
            legRow = fromRow + (toRow > fromRow ? 1 : -1);
            legCol = fromCol;
        } else {
            // 横走两格，竖走一格
            legRow = fromRow;
            legCol = fromCol + (toCol > fromCol ? 1 : -1);
        }
        
        return board[legRow][legCol] === null;
    }

    /**
     * 验证车的走法
     * @param {Array} board 棋盘状态
     * @param {Object} piece 棋子对象
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否合法
     */
    validateChariotMove(board, piece, fromRow, fromCol, toRow, toCol) {
        // 只能直线移动
        if (fromRow !== toRow && fromCol !== toCol) {
            return false;
        }
        
        // 检查路径是否被阻挡
        return !this.isPathBlocked(board, fromRow, fromCol, toRow, toCol);
    }

    /**
     * 验证炮的走法
     * @param {Array} board 棋盘状态
     * @param {Object} piece 棋子对象
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否合法
     */
    validateCannonMove(board, piece, fromRow, fromCol, toRow, toCol) {
        // 只能直线移动
        if (fromRow !== toRow && fromCol !== toCol) {
            return false;
        }
        
        const targetPiece = board[toRow][toCol];
        const piecesBetween = this.countPiecesBetween(board, fromRow, fromCol, toRow, toCol);
        
        if (targetPiece === null) {
            // 移动到空位，中间不能有棋子
            return piecesBetween === 0;
        } else {
            // 吃子，中间必须有且仅有一个棋子（炮架）
            return piecesBetween === 1;
        }
    }

    /**
     * 验证兵/卒的走法
     * @param {Array} board 棋盘状态
     * @param {Object} piece 棋子对象
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否合法
     */
    validatePawnMove(board, piece, fromRow, fromCol, toRow, toCol) {
        const rowDiff = toRow - fromRow;
        const colDiff = Math.abs(toCol - fromCol);
        
        // 只能走一格
        if (Math.abs(rowDiff) + colDiff !== 1) {
            return false;
        }
        
        if (piece.color === PieceColor.RED) {
            // 红兵：过河前只能向前，过河后可以左右移动
            if (fromRow <= this.RIVER_ROW) {
                // 未过河，只能向前
                return rowDiff === 1 && colDiff === 0;
            } else {
                // 已过河，可以向前或左右移动，但不能后退
                return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
            }
        } else {
            // 黑卒：过河前只能向前，过河后可以左右移动
            if (fromRow >= this.RIVER_ROW) {
                // 未过河，只能向前
                return rowDiff === -1 && colDiff === 0;
            } else {
                // 已过河，可以向前或左右移动，但不能后退
                return (rowDiff === -1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
            }
        }
    }

    /**
     * 验证走法是否合法
     * @param {Array} board 棋盘状态
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {boolean} 走法是否合法
     */
    isValidMove(board, fromRow, fromCol, toRow, toCol) {
        // 检查坐标是否在棋盘范围内
        if (!this.isInBoard(fromRow, fromCol) || !this.isInBoard(toRow, toCol)) {
            return false;
        }
        
        // 检查起始位置是否有棋子
        const piece = board[fromRow][fromCol];
        if (!piece) {
            return false;
        }
        
        // 检查是否移动到原位置
        if (fromRow === toRow && fromCol === toCol) {
            return false;
        }
        
        // 检查目标位置是否有己方棋子
        const targetPiece = board[toRow][toCol];
        if (targetPiece && targetPiece.color === piece.color) {
            return false;
        }
        
        // 根据棋子类型验证走法
        switch (piece.type) {
            case PieceType.KING:
                return this.validateKingMove(board, piece, fromRow, fromCol, toRow, toCol);
            case PieceType.ADVISOR:
                return this.validateAdvisorMove(board, piece, fromRow, fromCol, toRow, toCol);
            case PieceType.ELEPHANT:
                return this.validateElephantMove(board, piece, fromRow, fromCol, toRow, toCol);
            case PieceType.HORSE:
                return this.validateHorseMove(board, piece, fromRow, fromCol, toRow, toCol);
            case PieceType.CHARIOT:
                return this.validateChariotMove(board, piece, fromRow, fromCol, toRow, toCol);
            case PieceType.CANNON:
                return this.validateCannonMove(board, piece, fromRow, fromCol, toRow, toCol);
            case PieceType.PAWN:
                return this.validatePawnMove(board, piece, fromRow, fromCol, toRow, toCol);
            default:
                return false;
        }
    }

    /**
     * 获取棋子的所有合法走法
     * @param {Array} board 棋盘状态
     * @param {number} row 棋子行坐标
     * @param {number} col 棋子列坐标
     * @returns {Array} 合法走法列表
     */
    getPossibleMoves(board, row, col) {
        const moves = [];
        
        for (let toRow = 0; toRow < this.BOARD_HEIGHT; toRow++) {
            for (let toCol = 0; toCol < this.BOARD_WIDTH; toCol++) {
                if (this.isValidMove(board, row, col, toRow, toCol)) {
                    moves.push({ fromRow: row, fromCol: col, toRow, toCol });
                }
            }
        }
        
        return moves;
    }
}

// 导出类和常量
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ChessRules, PieceType, PieceColor, PieceDefinitions };
} else {
    window.ChessRules = ChessRules;
    window.PieceType = PieceType;
    window.PieceColor = PieceColor;
    window.PieceDefinitions = PieceDefinitions;
}
