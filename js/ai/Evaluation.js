/**
 * HuntGame - AI评估函数
 * 实现完整的象棋局面评估算法
 */

class Evaluation {
    constructor() {
        this.pieceValues = GameConfig.ai.pieceValues;
        this.evaluationWeights = GameConfig.ai.evaluation;

        // 位置价值表
        this.positionValues = this.initPositionValues();

        // 棋子保护关系
        this.protectionBonus = 10;
        this.attackBonus = 15;
    }

    /**
     * 初始化位置价值表
     * @returns {Object} 位置价值表
     */
    initPositionValues() {
        return {
            'pawn': {
                'red': [
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [20, 0, 20, 0, 20, 0, 20, 0, 20],
                    [30, 0, 30, 0, 30, 0, 30, 0, 30],
                    [40, 0, 40, 0, 40, 0, 40, 0, 40],
                    [50, 0, 50, 0, 50, 0, 50, 0, 50]
                ],
                'black': [
                    [50, 0, 50, 0, 50, 0, 50, 0, 50],
                    [40, 0, 40, 0, 40, 0, 40, 0, 40],
                    [30, 0, 30, 0, 30, 0, 30, 0, 30],
                    [20, 0, 20, 0, 20, 0, 20, 0, 20],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0]
                ]
            },
            'cannon': {
                'red': [
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 20, 0, 0, 0, 0, 0, 20, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0]
                ],
                'black': [
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 20, 0, 0, 0, 0, 0, 20, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0]
                ]
            },
            'chariot': {
                'red': [
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [20, 20, 20, 20, 20, 20, 20, 20, 20]
                ],
                'black': [
                    [20, 20, 20, 20, 20, 20, 20, 20, 20],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0]
                ]
            }
        };
    }

    /**
     * 评估棋盘局面
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 评估分数
     */
    evaluate(board, color) {
        let totalScore = 0;

        // 材质评估
        const materialScore = this.evaluateMaterial(board, color);
        totalScore += materialScore * this.evaluationWeights.material;

        // 位置评估
        const positionScore = this.evaluatePosition(board, color);
        totalScore += positionScore * this.evaluationWeights.position;

        // 机动性评估
        const mobilityScore = this.evaluateMobility(board, color);
        totalScore += mobilityScore * this.evaluationWeights.mobility;

        // 安全性评估
        const safetyScore = this.evaluateSafety(board, color);
        totalScore += safetyScore * this.evaluationWeights.safety;

        // 发展评估
        const developmentScore = this.evaluateDevelopment(board, color);
        totalScore += developmentScore * this.evaluationWeights.development;

        return Math.round(totalScore);
    }

    /**
     * 材质评估
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 材质分数
     */
    evaluateMaterial(board, color) {
        let score = 0;

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece) {
                    const value = this.pieceValues[piece.type] || 0;
                    if (piece.color === color) {
                        score += value;
                    } else {
                        score -= value;
                    }
                }
            }
        }

        return score;
    }

    /**
     * 位置评估
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 位置分数
     */
    evaluatePosition(board, color) {
        let score = 0;

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece && this.positionValues[piece.type]) {
                    const positionTable = this.positionValues[piece.type][piece.color];
                    if (positionTable && positionTable[row] && positionTable[row][col] !== undefined) {
                        const positionValue = positionTable[row][col];
                        if (piece.color === color) {
                            score += positionValue;
                        } else {
                            score -= positionValue;
                        }
                    }
                }
            }
        }

        return score;
    }

    /**
     * 机动性评估（可移动的位置数量）
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 机动性分数
     */
    evaluateMobility(board, color) {
        let score = 0;
        const rules = new ChessRules();

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece) {
                    const moves = rules.getPossibleMoves(board, row, col);
                    const mobilityValue = moves.length * 2; // 每个可能的移动值2分

                    if (piece.color === color) {
                        score += mobilityValue;
                    } else {
                        score -= mobilityValue;
                    }
                }
            }
        }

        return score;
    }

    /**
     * 安全性评估（王的安全、棋子保护等）
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 安全性分数
     */
    evaluateSafety(board, color) {
        let score = 0;

        // 评估王的安全
        score += this.evaluateKingSafety(board, color);

        // 评估棋子保护
        score += this.evaluatePieceProtection(board, color);

        return score;
    }

    /**
     * 王的安全评估
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 王安全分数
     */
    evaluateKingSafety(board, color) {
        let score = 0;
        const rules = new ChessRules();
        const opponentColor = color === 'red' ? 'black' : 'red';

        // 找到己方和对方的王
        let myKing = null;
        let opponentKing = null;

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece && piece.type === 'king') {
                    if (piece.color === color) {
                        myKing = { row, col };
                    } else {
                        opponentKing = { row, col };
                    }
                }
            }
        }

        if (myKing) {
            // 检查己方王是否被攻击
            let attackerCount = 0;
            for (let row = 0; row < 10; row++) {
                for (let col = 0; col < 9; col++) {
                    const piece = board[row][col];
                    if (piece && piece.color === opponentColor) {
                        if (rules.isValidMove(board, row, col, myKing.row, myKing.col)) {
                            attackerCount++;
                        }
                    }
                }
            }

            // 被攻击的王扣分
            score -= attackerCount * 50;

            // 王周围的保护棋子加分
            const protectors = this.countProtectors(board, myKing.row, myKing.col, color);
            score += protectors * 20;
        }

        if (opponentKing) {
            // 检查对方王是否被攻击（对我方有利）
            let attackerCount = 0;
            for (let row = 0; row < 10; row++) {
                for (let col = 0; col < 9; col++) {
                    const piece = board[row][col];
                    if (piece && piece.color === color) {
                        if (rules.isValidMove(board, row, col, opponentKing.row, opponentKing.col)) {
                            attackerCount++;
                        }
                    }
                }
            }

            // 攻击对方王加分
            score += attackerCount * 30;
        }

        return score;
    }

    /**
     * 棋子保护评估
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 保护分数
     */
    evaluatePieceProtection(board, color) {
        let score = 0;

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece) {
                    const protectors = this.countProtectors(board, row, col, piece.color);
                    const attackers = this.countAttackers(board, row, col, piece.color);

                    if (piece.color === color) {
                        // 己方棋子被保护加分，被攻击扣分
                        score += protectors * this.protectionBonus;
                        score -= attackers * this.attackBonus;
                    } else {
                        // 对方棋子被保护扣分，被攻击加分
                        score -= protectors * this.protectionBonus;
                        score += attackers * this.attackBonus;
                    }
                }
            }
        }

        return score;
    }

    /**
     * 计算保护某个位置的己方棋子数量
     * @param {Array} board 棋盘状态
     * @param {number} row 目标行
     * @param {number} col 目标列
     * @param {string} color 棋子颜色
     * @returns {number} 保护者数量
     */
    countProtectors(board, row, col, color) {
        let count = 0;
        const rules = new ChessRules();

        for (let r = 0; r < 10; r++) {
            for (let c = 0; c < 9; c++) {
                const piece = board[r][c];
                if (piece && piece.color === color && (r !== row || c !== col)) {
                    if (rules.isValidMove(board, r, c, row, col)) {
                        count++;
                    }
                }
            }
        }

        return count;
    }

    /**
     * 计算攻击某个位置的对方棋子数量
     * @param {Array} board 棋盘状态
     * @param {number} row 目标行
     * @param {number} col 目标列
     * @param {string} color 棋子颜色
     * @returns {number} 攻击者数量
     */
    countAttackers(board, row, col, color) {
        let count = 0;
        const rules = new ChessRules();
        const opponentColor = color === 'red' ? 'black' : 'red';

        for (let r = 0; r < 10; r++) {
            for (let c = 0; c < 9; c++) {
                const piece = board[r][c];
                if (piece && piece.color === opponentColor) {
                    if (rules.isValidMove(board, r, c, row, col)) {
                        count++;
                    }
                }
            }
        }

        return count;
    }

    /**
     * 发展评估（棋子是否离开初始位置等）
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 发展分数
     */
    evaluateDevelopment(board, color) {
        let score = 0;

        // 检查马和炮是否已经发展
        const developmentPositions = color === 'red' ?
            [[9, 1], [9, 7], [7, 1], [7, 7]] : // 红方马和炮的初始位置
            [[0, 1], [0, 7], [2, 1], [2, 7]];  // 黑方马和炮的初始位置

        for (const [row, col] of developmentPositions) {
            const piece = board[row][col];
            if (!piece || piece.color !== color ||
                (piece.type !== 'horse' && piece.type !== 'cannon')) {
                // 如果马或炮已经离开初始位置，加分
                score += 15;
            }
        }

        // 检查中心控制
        const centerPositions = [[4, 4], [5, 4]]; // 棋盘中心位置
        for (const [row, col] of centerPositions) {
            const attackers = this.countAttackers(board, row, col,
                color === 'red' ? 'black' : 'red');
            score += attackers * 5; // 控制中心加分
        }

        return score;
    }

    /**
     * 快速材质评估（用于搜索剪枝）
     * @param {Array} board 棋盘状态
     * @param {string} color 评估方颜色
     * @returns {number} 材质分数
     */
    quickEvaluate(board, color) {
        return this.evaluateMaterial(board, color);
    }

    /**
     * 检查是否为终局
     * @param {Array} board 棋盘状态
     * @returns {boolean} 是否为终局
     */
    isEndgame(board) {
        let pieceCount = 0;
        let majorPieces = 0; // 车、马、炮的数量

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece) {
                    pieceCount++;
                    if (piece.type === 'chariot' ||
                        piece.type === 'horse' ||
                        piece.type === 'cannon') {
                        majorPieces++;
                    }
                }
            }
        }

        // 如果总棋子数少于20个或主要棋子少于8个，认为是终局
        return pieceCount < 20 || majorPieces < 8;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Evaluation;
} else {
    window.Evaluation = Evaluation;
}
