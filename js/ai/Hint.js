/**
 * HuntGame - AI提示系统
 * 完整的智能提示和分析系统
 */

class HintSystem {
    constructor() {
        this.isEnabled = true;
        this.evaluation = new Evaluation();
        this.searchEngine = new SearchEngine();
        this.analysisDepth = 3;
        this.maxHints = 3;

        // 提示类型权重
        this.hintWeights = {
            tactical: 1.0,      // 战术提示
            positional: 0.7,    // 位置提示
            defensive: 0.9,     // 防守提示
            development: 0.5,   // 发展提示
            endgame: 0.8       // 终局提示
        };

        console.log('Hint system initialized');
    }

    /**
     * 获取智能提示
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @param {Object} gameInfo 游戏信息
     * @returns {Object|null} 提示信息
     */
    async getHint(board, color, gameInfo = {}) {
        if (!this.isEnabled) {
            return null;
        }

        try {
            // 获取所有可能的走法
            const moves = this.searchEngine.generateMoves(board, color);
            if (moves.length === 0) {
                return {
                    type: 'no_moves',
                    description: '没有可用的走法',
                    move: null,
                    analysis: null
                };
            }

            // 分析当前局面
            const positionAnalysis = this.analyzePosition(board, color, gameInfo);

            // 获取最佳走法建议
            const bestMoves = await this.getBestMoves(board, color, this.maxHints);

            // 生成提示
            const hints = this.generateHints(bestMoves, positionAnalysis, board, color);

            return {
                type: 'analysis',
                description: this.getMainHintDescription(hints[0]),
                move: hints[0].move,
                analysis: positionAnalysis,
                hints: hints,
                confidence: this.calculateConfidence(hints[0])
            };

        } catch (error) {
            console.error('Error generating hint:', error);
            return {
                type: 'error',
                description: '提示生成失败',
                move: null,
                analysis: null
            };
        }
    }

    /**
     * 分析当前局面
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @param {Object} gameInfo 游戏信息
     * @returns {Object} 局面分析
     */
    analyzePosition(board, color, gameInfo) {
        const analysis = {
            score: this.evaluation.evaluate(board, color),
            phase: this.getGamePhase(board),
            threats: this.findThreats(board, color),
            opportunities: this.findOpportunities(board, color),
            weaknesses: this.findWeaknesses(board, color),
            kingSafety: this.analyzeKingSafety(board, color),
            materialBalance: this.analyzeMaterial(board, color),
            positionalFactors: this.analyzePositionalFactors(board, color)
        };

        // 添加局面评价
        analysis.evaluation = this.getPositionEvaluation(analysis.score);

        return analysis;
    }

    /**
     * 获取最佳走法
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @param {number} count 返回数量
     * @returns {Array} 最佳走法列表
     */
    async getBestMoves(board, color, count = 3) {
        // 使用搜索引擎分析走法
        const searchResult = this.searchEngine.searchBestMove(board, color, this.analysisDepth, 2000);

        if (!searchResult || !searchResult.move) {
            return [];
        }

        // 获取所有走法并评估
        const moves = this.searchEngine.generateMoves(board, color);
        const evaluatedMoves = [];

        for (const move of moves.slice(0, 10)) { // 限制分析数量
            const newBoard = this.searchEngine.makeMove(board, move);
            const score = this.evaluation.evaluate(newBoard, color);

            evaluatedMoves.push({
                move: move,
                score: score,
                type: this.classifyMove(move, board),
                description: this.describeMoveDetailed(move, board)
            });
        }

        // 按分数排序并返回前几个
        evaluatedMoves.sort((a, b) => b.score - a.score);
        return evaluatedMoves.slice(0, count);
    }

    /**
     * 生成提示信息
     * @param {Array} bestMoves 最佳走法列表
     * @param {Object} analysis 局面分析
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {Array} 提示列表
     */
    generateHints(bestMoves, analysis, board, color) {
        const hints = [];

        for (const moveData of bestMoves) {
            const hint = {
                move: moveData.move,
                score: moveData.score,
                type: moveData.type,
                description: moveData.description,
                reasoning: this.generateReasoning(moveData, analysis, board, color),
                priority: this.calculatePriority(moveData, analysis)
            };

            hints.push(hint);
        }

        // 按优先级排序
        hints.sort((a, b) => b.priority - a.priority);

        return hints;
    }

    /**
     * 生成走法推理
     * @param {Object} moveData 走法数据
     * @param {Object} analysis 局面分析
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {string} 推理说明
     */
    generateReasoning(moveData, analysis, board, color) {
        const reasons = [];

        // 根据走法类型生成推理
        switch (moveData.type) {
            case 'capture':
                reasons.push('可以吃掉对方棋子，获得材质优势');
                break;
            case 'check':
                reasons.push('将军对方，迫使对方应对');
                break;
            case 'defense':
                reasons.push('防守重要位置，保护己方棋子');
                break;
            case 'development':
                reasons.push('发展棋子，改善位置');
                break;
            case 'control':
                reasons.push('控制关键位置，限制对方行动');
                break;
        }

        // 根据局面分析添加推理
        if (analysis.threats.length > 0) {
            reasons.push('应对当前威胁');
        }

        if (analysis.opportunities.length > 0) {
            reasons.push('利用战术机会');
        }

        if (moveData.score > analysis.score + 50) {
            reasons.push('显著改善局面评估');
        }

        return reasons.join('，') || '改善整体局面';
    }

    /**
     * 计算提示优先级
     * @param {Object} moveData 走法数据
     * @param {Object} analysis 局面分析
     * @returns {number} 优先级分数
     */
    calculatePriority(moveData, analysis) {
        let priority = moveData.score;

        // 根据走法类型调整优先级
        const typeWeight = this.hintWeights[moveData.type] || 0.5;
        priority *= typeWeight;

        // 根据局面情况调整
        if (analysis.threats.length > 0) {
            priority += 100; // 有威胁时提高防守走法优先级
        }

        if (analysis.kingSafety < 0) {
            priority += 50; // 王不安全时提高防守优先级
        }

        return priority;
    }

    /**
     * 获取游戏阶段
     * @param {Array} board 棋盘状态
     * @returns {string} 游戏阶段
     */
    getGamePhase(board) {
        let pieceCount = 0;
        let majorPieces = 0;

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece) {
                    pieceCount++;
                    if (['chariot', 'horse', 'cannon'].includes(piece.type)) {
                        majorPieces++;
                    }
                }
            }
        }

        if (pieceCount > 24) return 'opening';
        if (pieceCount > 16) return 'middlegame';
        return 'endgame';
    }

    /**
     * 寻找威胁
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {Array} 威胁列表
     */
    findThreats(board, color) {
        const threats = [];
        const opponentColor = color === 'red' ? 'black' : 'red';
        const rules = new ChessRules();

        // 检查己方棋子是否受到威胁
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece && piece.color === color) {
                    // 检查是否被对方攻击
                    for (let r = 0; r < 10; r++) {
                        for (let c = 0; c < 9; c++) {
                            const attacker = board[r][c];
                            if (attacker && attacker.color === opponentColor) {
                                if (rules.isValidMove(board, r, c, row, col)) {
                                    threats.push({
                                        target: { row, col, piece },
                                        attacker: { row: r, col: c, piece: attacker },
                                        severity: this.getThreatSeverity(piece, attacker)
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }

        return threats.sort((a, b) => b.severity - a.severity);
    }

    /**
     * 寻找机会
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {Array} 机会列表
     */
    findOpportunities(board, color) {
        const opportunities = [];
        const rules = new ChessRules();

        // 寻找可以吃子的机会
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece && piece.color === color) {
                    // 检查可以攻击的目标
                    for (let r = 0; r < 10; r++) {
                        for (let c = 0; c < 9; c++) {
                            const target = board[r][c];
                            if (target && target.color !== color) {
                                if (rules.isValidMove(board, row, col, r, c)) {
                                    opportunities.push({
                                        attacker: { row, col, piece },
                                        target: { row: r, col: c, piece: target },
                                        value: this.getPieceValue(target.type),
                                        type: 'capture'
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }

        return opportunities.sort((a, b) => b.value - a.value);
    }

    /**
     * 寻找弱点
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {Array} 弱点列表
     */
    findWeaknesses(board, color) {
        const weaknesses = [];

        // 检查无保护的棋子
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece && piece.color === color) {
                    if (!this.isPieceProtected(board, row, col, color)) {
                        weaknesses.push({
                            position: { row, col },
                            piece: piece,
                            type: 'unprotected',
                            severity: this.getPieceValue(piece.type)
                        });
                    }
                }
            }
        }

        return weaknesses.sort((a, b) => b.severity - a.severity);
    }

    /**
     * 分析王的安全性
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {number} 安全性分数
     */
    analyzeKingSafety(board, color) {
        const kingPos = this.findKing(board, color);
        if (!kingPos) return -1000;

        let safety = 0;

        // 检查王周围的保护
        const protectors = this.countProtectors(board, kingPos.row, kingPos.col, color);
        safety += protectors * 10;

        // 检查是否被将军
        if (this.isInCheck(board, color)) {
            safety -= 50;
        }

        // 检查逃跑路线
        const escapeRoutes = this.countEscapeRoutes(board, kingPos.row, kingPos.col, color);
        safety += escapeRoutes * 5;

        return safety;
    }

    /**
     * 分析材质平衡
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {Object} 材质分析
     */
    analyzeMaterial(board, color) {
        const material = { own: 0, opponent: 0 };

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece) {
                    const value = this.getPieceValue(piece.type);
                    if (piece.color === color) {
                        material.own += value;
                    } else {
                        material.opponent += value;
                    }
                }
            }
        }

        return {
            own: material.own,
            opponent: material.opponent,
            balance: material.own - material.opponent,
            advantage: material.own > material.opponent ? 'own' :
                      material.opponent > material.own ? 'opponent' : 'equal'
        };
    }

    /**
     * 分析位置因素
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {Object} 位置分析
     */
    analyzePositionalFactors(board, color) {
        return {
            centerControl: this.analyzeCenterControl(board, color),
            development: this.analyzeDevelopment(board, color),
            coordination: this.analyzeCoordination(board, color),
            mobility: this.analyzeMobility(board, color)
        };
    }

    /**
     * 分类走法
     * @param {Object} move 走法
     * @param {Array} board 棋盘状态
     * @returns {string} 走法类型
     */
    classifyMove(move, board) {
        const targetPiece = board[move.to.row][move.to.col];

        if (targetPiece) {
            return 'capture';
        }

        // 检查是否将军
        const newBoard = this.searchEngine.makeMove(board, move);
        const opponentColor = board[move.from.row][move.from.col].color === 'red' ? 'black' : 'red';
        if (this.isInCheck(newBoard, opponentColor)) {
            return 'check';
        }

        // 其他分类逻辑
        if (this.isDefensiveMove(move, board)) {
            return 'defense';
        }

        if (this.isDevelopmentMove(move, board)) {
            return 'development';
        }

        return 'positional';
    }

    /**
     * 详细描述走法
     * @param {Object} move 走法
     * @param {Array} board 棋盘状态
     * @returns {string} 走法描述
     */
    describeMoveDetailed(move, board) {
        const piece = board[move.from.row][move.from.col];
        const target = board[move.to.row][move.to.col];

        const pieceNames = {
            'general': '将',
            'advisor': '士',
            'elephant': '象',
            'horse': '马',
            'chariot': '车',
            'cannon': '炮',
            'soldier': '兵'
        };

        const pieceName = pieceNames[piece.type] || piece.type;
        const fromPos = `${String.fromCharCode(97 + move.from.col)}${10 - move.from.row}`;
        const toPos = `${String.fromCharCode(97 + move.to.col)}${10 - move.to.row}`;

        if (target) {
            const targetName = pieceNames[target.type] || target.type;
            return `${pieceName}${fromPos}吃${targetName}${toPos}`;
        } else {
            return `${pieceName}${fromPos}到${toPos}`;
        }
    }

    /**
     * 获取主要提示描述
     * @param {Object} hint 提示对象
     * @returns {string} 描述
     */
    getMainHintDescription(hint) {
        if (!hint) return '暂无可用提示';

        const confidence = this.calculateConfidence(hint);
        const confidenceText = confidence > 0.8 ? '强烈建议' :
                              confidence > 0.6 ? '建议' : '可以考虑';

        return `${confidenceText}：${hint.description}`;
    }

    /**
     * 计算提示置信度
     * @param {Object} hint 提示对象
     * @returns {number} 置信度 (0-1)
     */
    calculateConfidence(hint) {
        if (!hint) return 0;

        let confidence = 0.5;

        // 根据分数差异调整置信度
        if (hint.score > 100) confidence += 0.3;
        else if (hint.score > 50) confidence += 0.2;
        else if (hint.score > 0) confidence += 0.1;

        // 根据走法类型调整
        if (hint.type === 'capture') confidence += 0.2;
        else if (hint.type === 'check') confidence += 0.15;
        else if (hint.type === 'defense') confidence += 0.1;

        return Math.min(confidence, 1.0);
    }

    /**
     * 获取局面评价
     * @param {number} score 评估分数
     * @returns {string} 评价文本
     */
    getPositionEvaluation(score) {
        if (score > 200) return '优势很大';
        if (score > 100) return '优势明显';
        if (score > 50) return '略有优势';
        if (score > -50) return '局面均衡';
        if (score > -100) return '略处劣势';
        if (score > -200) return '劣势明显';
        return '劣势很大';
    }

    /**
     * 获取威胁严重程度
     * @param {Object} target 目标棋子
     * @param {Object} attacker 攻击棋子
     * @returns {number} 严重程度
     */
    getThreatSeverity(target, attacker) {
        const targetValue = this.getPieceValue(target.type);
        const attackerValue = this.getPieceValue(attacker.type);

        // 如果目标价值高于攻击者，威胁更严重
        return targetValue + (targetValue > attackerValue ? 50 : 0);
    }

    /**
     * 获取棋子价值
     * @param {string} pieceType 棋子类型
     * @returns {number} 价值
     */
    getPieceValue(pieceType) {
        const values = {
            'soldier': 10,
            'cannon': 45,
            'chariot': 90,
            'horse': 40,
            'elephant': 20,
            'advisor': 20,
            'general': 1000
        };
        return values[pieceType] || 0;
    }

    /**
     * 检查棋子是否受保护
     * @param {Array} board 棋盘状态
     * @param {number} row 行
     * @param {number} col 列
     * @param {string} color 颜色
     * @returns {boolean} 是否受保护
     */
    isPieceProtected(board, row, col, color) {
        const rules = new ChessRules();

        for (let r = 0; r < 10; r++) {
            for (let c = 0; c < 9; c++) {
                const piece = board[r][c];
                if (piece && piece.color === color && (r !== row || c !== col)) {
                    if (rules.isValidMove(board, r, c, row, col)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 寻找王的位置
     * @param {Array} board 棋盘状态
     * @param {string} color 颜色
     * @returns {Object|null} 王的位置
     */
    findKing(board, color) {
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece && piece.type === 'general' && piece.color === color) {
                    return { row, col };
                }
            }
        }
        return null;
    }

    /**
     * 检查是否被将军
     * @param {Array} board 棋盘状态
     * @param {string} color 颜色
     * @returns {boolean} 是否被将军
     */
    isInCheck(board, color) {
        const kingPos = this.findKing(board, color);
        if (!kingPos) return false;

        const opponentColor = color === 'red' ? 'black' : 'red';
        const rules = new ChessRules();

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece && piece.color === opponentColor) {
                    if (rules.isValidMove(board, row, col, kingPos.row, kingPos.col)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 计算保护者数量
     * @param {Array} board 棋盘状态
     * @param {number} row 行
     * @param {number} col 列
     * @param {string} color 颜色
     * @returns {number} 保护者数量
     */
    countProtectors(board, row, col, color) {
        let count = 0;
        const rules = new ChessRules();

        for (let r = 0; r < 10; r++) {
            for (let c = 0; c < 9; c++) {
                const piece = board[r][c];
                if (piece && piece.color === color && (r !== row || c !== col)) {
                    if (rules.isValidMove(board, r, c, row, col)) {
                        count++;
                    }
                }
            }
        }

        return count;
    }

    /**
     * 计算逃跑路线数量
     * @param {Array} board 棋盘状态
     * @param {number} row 行
     * @param {number} col 列
     * @param {string} color 颜色
     * @returns {number} 逃跑路线数量
     */
    countEscapeRoutes(board, row, col, color) {
        let count = 0;
        const rules = new ChessRules();
        const piece = board[row][col];

        // 检查王可以移动的位置
        const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]];

        for (const [dr, dc] of directions) {
            const newRow = row + dr;
            const newCol = col + dc;

            if (rules.isValidMove(board, row, col, newRow, newCol)) {
                // 检查移动后是否安全
                const testBoard = JSON.parse(JSON.stringify(board));
                testBoard[newRow][newCol] = piece;
                testBoard[row][col] = null;

                if (!this.isInCheck(testBoard, color)) {
                    count++;
                }
            }
        }

        return count;
    }

    /**
     * 分析中心控制
     * @param {Array} board 棋盘状态
     * @param {string} color 颜色
     * @returns {number} 中心控制分数
     */
    analyzeCenterControl(board, color) {
        // 中心区域位置
        const centerSquares = [
            [4, 3], [4, 4], [4, 5],
            [5, 3], [5, 4], [5, 5]
        ];

        let control = 0;
        const rules = new ChessRules();

        for (const [centerRow, centerCol] of centerSquares) {
            let attackers = 0;

            for (let row = 0; row < 10; row++) {
                for (let col = 0; col < 9; col++) {
                    const piece = board[row][col];
                    if (piece && piece.color === color) {
                        if (rules.isValidMove(board, row, col, centerRow, centerCol)) {
                            attackers++;
                        }
                    }
                }
            }

            control += attackers;
        }

        return control;
    }

    /**
     * 分析发展程度
     * @param {Array} board 棋盘状态
     * @param {string} color 颜色
     * @returns {number} 发展分数
     */
    analyzeDevelopment(board, color) {
        let development = 0;
        const startRow = color === 'red' ? 9 : 0;

        // 检查主要棋子是否离开起始位置
        for (let col = 0; col < 9; col++) {
            const piece = board[startRow][col];
            if (!piece || piece.color !== color) {
                development += 10; // 棋子已移动
            }
        }

        return development;
    }

    /**
     * 分析协调性
     * @param {Array} board 棋盘状态
     * @param {string} color 颜色
     * @returns {number} 协调分数
     */
    analyzeCoordination(board, color) {
        let coordination = 0;
        const rules = new ChessRules();

        // 计算棋子间的相互保护
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece && piece.color === color) {
                    const protectors = this.countProtectors(board, row, col, color);
                    coordination += protectors * 5;
                }
            }
        }

        return coordination;
    }

    /**
     * 分析机动性
     * @param {Array} board 棋盘状态
     * @param {string} color 颜色
     * @returns {number} 机动性分数
     */
    analyzeMobility(board, color) {
        const moves = this.searchEngine.generateMoves(board, color);
        return moves.length;
    }

    /**
     * 检查是否为防守走法
     * @param {Object} move 走法
     * @param {Array} board 棋盘状态
     * @returns {boolean} 是否为防守走法
     */
    isDefensiveMove(move, board) {
        // 简化的防守检测逻辑
        const piece = board[move.from.row][move.from.col];
        const threats = this.findThreats(board, piece.color);

        // 检查是否移动到更安全的位置
        return threats.some(threat =>
            threat.target.row === move.from.row &&
            threat.target.col === move.from.col
        );
    }

    /**
     * 检查是否为发展走法
     * @param {Object} move 走法
     * @param {Array} board 棋盘状态
     * @returns {boolean} 是否为发展走法
     */
    isDevelopmentMove(move, board) {
        const piece = board[move.from.row][move.from.col];
        const startRow = piece.color === 'red' ? 9 : 0;

        // 检查是否从起始位置移动
        return move.from.row === startRow;
    }

    /**
     * 启用/禁用提示
     * @param {boolean} enabled 是否启用
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        console.log(`Hint system ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * 设置分析深度
     * @param {number} depth 分析深度
     */
    setAnalysisDepth(depth) {
        this.analysisDepth = Math.max(1, Math.min(depth, 5));
        console.log(`Hint analysis depth set to ${this.analysisDepth}`);
    }

    /**
     * 设置最大提示数量
     * @param {number} count 最大提示数量
     */
    setMaxHints(count) {
        this.maxHints = Math.max(1, Math.min(count, 5));
        console.log(`Max hints set to ${this.maxHints}`);
    }

    /**
     * 获取系统状态
     * @returns {Object} 系统状态
     */
    getStatus() {
        return {
            isEnabled: this.isEnabled,
            analysisDepth: this.analysisDepth,
            maxHints: this.maxHints
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HintSystem;
} else {
    window.HintSystem = HintSystem;
}
