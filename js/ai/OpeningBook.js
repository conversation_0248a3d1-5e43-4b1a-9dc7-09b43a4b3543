/**
 * HuntGame - 开局库
 * 象棋开局库实现
 */

class OpeningBook {
    constructor() {
        this.openings = this.initializeOpenings();
        this.isEnabled = true;
    }

    /**
     * 初始化开局库
     * @returns {Object} 开局库数据
     */
    initializeOpenings() {
        return {
            // 中炮局
            'cannon_center': {
                name: '中炮局',
                moves: [
                    // 红方先手
                    { from: { row: 7, col: 4 }, to: { row: 4, col: 4 }, color: 'red' }, // 炮二平五
                    // 黑方应对
                    { from: { row: 2, col: 4 }, to: { row: 5, col: 4 }, color: 'black' }, // 炮8平5
                    // 红方继续
                    { from: { row: 9, col: 1 }, to: { row: 7, col: 2 }, color: 'red' }, // 马二进三
                    { from: { row: 0, col: 1 }, to: { row: 2, col: 2 }, color: 'black' }, // 马8进7
                ]
            },
            
            // 屏风马
            'screen_horse': {
                name: '屏风马',
                moves: [
                    { from: { row: 7, col: 4 }, to: { row: 4, col: 4 }, color: 'red' }, // 炮二平五
                    { from: { row: 0, col: 1 }, to: { row: 2, col: 2 }, color: 'black' }, // 马8进7
                    { from: { row: 9, col: 1 }, to: { row: 7, col: 2 }, color: 'red' }, // 马二进三
                    { from: { row: 2, col: 1 }, to: { row: 4, col: 1 }, color: 'black' }, // 炮2平1
                ]
            },
            
            // 飞相局
            'flying_elephant': {
                name: '飞相局',
                moves: [
                    { from: { row: 9, col: 2 }, to: { row: 7, col: 4 }, color: 'red' }, // 相三进五
                    { from: { row: 2, col: 4 }, to: { row: 5, col: 4 }, color: 'black' }, // 炮8平5
                    { from: { row: 9, col: 1 }, to: { row: 7, col: 2 }, color: 'red' }, // 马二进三
                    { from: { row: 0, col: 1 }, to: { row: 2, col: 2 }, color: 'black' }, // 马8进7
                ]
            },
            
            // 仙人指路
            'immortal_guides': {
                name: '仙人指路',
                moves: [
                    { from: { row: 6, col: 2 }, to: { row: 5, col: 2 }, color: 'red' }, // 兵三进一
                    { from: { row: 2, col: 4 }, to: { row: 5, col: 4 }, color: 'black' }, // 炮8平5
                    { from: { row: 9, col: 1 }, to: { row: 7, col: 2 }, color: 'red' }, // 马二进三
                    { from: { row: 0, col: 1 }, to: { row: 2, col: 2 }, color: 'black' }, // 马8进7
                ]
            }
        };
    }

    /**
     * 获取开局走法
     * @param {Array} board 当前棋盘状态
     * @param {string} color 当前玩家颜色
     * @param {Array} moveHistory 走法历史
     * @returns {Object|null} 开局走法
     */
    getOpeningMove(board, color, moveHistory = []) {
        if (!this.isEnabled || moveHistory.length >= 8) {
            return null;
        }

        // 如果是开局第一步（红方）
        if (moveHistory.length === 0 && color === 'red') {
            return this.getFirstMove();
        }

        // 根据历史走法匹配开局
        for (const [openingKey, opening] of Object.entries(this.openings)) {
            const matchingMove = this.findMatchingMove(opening, moveHistory, color);
            if (matchingMove) {
                console.log(`Using opening: ${opening.name}`);
                return matchingMove;
            }
        }

        return null;
    }

    /**
     * 获取开局第一步
     * @returns {Object} 第一步走法
     */
    getFirstMove() {
        const firstMoves = [
            // 中炮
            { from: { row: 7, col: 4 }, to: { row: 4, col: 4 }, weight: 40 },
            // 仙人指路
            { from: { row: 6, col: 2 }, to: { row: 5, col: 2 }, weight: 25 },
            { from: { row: 6, col: 6 }, to: { row: 5, col: 6 }, weight: 25 },
            // 飞相
            { from: { row: 9, col: 2 }, to: { row: 7, col: 4 }, weight: 10 }
        ];

        return this.selectWeightedMove(firstMoves);
    }

    /**
     * 根据权重选择走法
     * @param {Array} moves 带权重的走法列表
     * @returns {Object} 选择的走法
     */
    selectWeightedMove(moves) {
        const totalWeight = moves.reduce((sum, move) => sum + move.weight, 0);
        let random = Math.random() * totalWeight;

        for (const move of moves) {
            random -= move.weight;
            if (random <= 0) {
                return {
                    from: move.from,
                    to: move.to
                };
            }
        }

        // 如果没有选中，返回第一个
        return {
            from: moves[0].from,
            to: moves[0].to
        };
    }

    /**
     * 查找匹配的开局走法
     * @param {Object} opening 开局数据
     * @param {Array} moveHistory 走法历史
     * @param {string} color 当前玩家颜色
     * @returns {Object|null} 匹配的走法
     */
    findMatchingMove(opening, moveHistory, color) {
        const moves = opening.moves;
        
        // 检查历史走法是否与开局匹配
        for (let i = 0; i < Math.min(moveHistory.length, moves.length); i++) {
            const historyMove = moveHistory[i];
            const openingMove = moves[i];
            
            if (!this.movesMatch(historyMove, openingMove)) {
                return null; // 不匹配，这个开局不适用
            }
        }

        // 如果历史走法都匹配，返回下一步走法
        if (moveHistory.length < moves.length) {
            const nextMove = moves[moveHistory.length];
            if (nextMove.color === color) {
                return {
                    from: nextMove.from,
                    to: nextMove.to
                };
            }
        }

        return null;
    }

    /**
     * 检查两个走法是否匹配
     * @param {Object} move1 走法1
     * @param {Object} move2 走法2
     * @returns {boolean} 是否匹配
     */
    movesMatch(move1, move2) {
        return move1.from.row === move2.from.row &&
               move1.from.col === move2.from.col &&
               move1.to.row === move2.to.row &&
               move1.to.col === move2.to.col;
    }

    /**
     * 添加新的开局
     * @param {string} key 开局键名
     * @param {Object} opening 开局数据
     */
    addOpening(key, opening) {
        this.openings[key] = opening;
    }

    /**
     * 移除开局
     * @param {string} key 开局键名
     */
    removeOpening(key) {
        delete this.openings[key];
    }

    /**
     * 获取所有开局名称
     * @returns {Array} 开局名称列表
     */
    getOpeningNames() {
        return Object.values(this.openings).map(opening => opening.name);
    }

    /**
     * 启用/禁用开局库
     * @param {boolean} enabled 是否启用
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }

    /**
     * 检查是否启用
     * @returns {boolean} 是否启用
     */
    isOpeningBookEnabled() {
        return this.isEnabled;
    }

    /**
     * 获取开局统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalOpenings: Object.keys(this.openings).length,
            isEnabled: this.isEnabled,
            openingNames: this.getOpeningNames()
        };
    }

    /**
     * 从JSON数据加载开局库
     * @param {Object} data JSON数据
     */
    loadFromJSON(data) {
        if (data.openings) {
            this.openings = data.openings;
        }
        if (data.isEnabled !== undefined) {
            this.isEnabled = data.isEnabled;
        }
    }

    /**
     * 导出为JSON数据
     * @returns {Object} JSON数据
     */
    toJSON() {
        return {
            openings: this.openings,
            isEnabled: this.isEnabled
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OpeningBook;
} else {
    window.OpeningBook = OpeningBook;
}
