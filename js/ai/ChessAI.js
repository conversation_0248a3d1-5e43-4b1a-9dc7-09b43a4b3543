/**
 * HuntGame - 象棋AI引擎
 * 完整的象棋AI引擎实现
 */

class ChessAI {
    constructor() {
        this.evaluation = new Evaluation();
        this.searchEngine = new SearchEngine();
        this.difficulty = new DifficultyController();
        this.hintSystem = new HintSystem();
        this.openingBook = new OpeningBook();
        this.isInitialized = true;

        // AI状态
        this.isThinking = false;
        this.thinkingStartTime = 0;
        this.lastMove = null;

        // 性能统计
        this.stats = {
            movesPlayed: 0,
            averageThinkTime: 0,
            totalThinkTime: 0,
            bestMoveAccuracy: 0
        };

        console.log('Chess AI engine initialized');
    }

    /**
     * 设置难度
     * @param {string} level 难度级别
     */
    setDifficulty(level) {
        this.difficulty.setDifficulty(level);

        // 根据难度调整搜索参数
        const settings = this.difficulty.getCurrentSettings();
        this.searchEngine.setParameters({
            maxDepth: settings.searchDepth,
            maxTime: settings.thinkTime
        });

        console.log(`AI difficulty set to ${level}`);
    }

    /**
     * 获取AI走法
     * @param {Array} board 棋盘状态
     * @param {string} color AI颜色
     * @param {Object} gameInfo 游戏信息
     * @returns {Promise<Object|null>} AI走法
     */
    async getMove(board, color, gameInfo = {}) {
        if (this.isThinking) {
            console.warn('AI is already thinking');
            return null;
        }

        this.isThinking = true;
        this.thinkingStartTime = Date.now();

        try {
            let move = null;

            // 1. 检查开局库
            if (gameInfo.moveCount < 10) {
                move = this.openingBook.getOpeningMove(board, color, gameInfo.moveHistory);
                if (move) {
                    console.log('Using opening book move');
                    return this.finishThinking(move, 'opening');
                }
            }

            // 2. 检查是否有必胜走法
            move = this.findWinningMove(board, color);
            if (move) {
                console.log('Found winning move');
                return this.finishThinking(move, 'winning');
            }

            // 3. 检查是否需要防守
            move = this.findDefensiveMove(board, color);
            if (move) {
                console.log('Found defensive move');
                return this.finishThinking(move, 'defensive');
            }

            // 4. 使用搜索引擎寻找最佳走法
            const settings = this.difficulty.getCurrentSettings();
            const searchResult = this.searchEngine.searchBestMove(
                board,
                color,
                settings.searchDepth,
                settings.thinkTime
            );

            if (searchResult && searchResult.move) {
                console.log(`AI found move with score ${searchResult.score}`);
                console.log(`Search stats:`, searchResult.stats);
                return this.finishThinking(searchResult.move, 'search', searchResult);
            }

            // 5. 如果搜索失败，随机选择一个合法走法
            move = this.getRandomMove(board, color);
            if (move) {
                console.log('Using random move as fallback');
                return this.finishThinking(move, 'random');
            }

            console.warn('No valid moves found');
            return null;

        } catch (error) {
            console.error('Error in AI getMove:', error);
            return null;
        } finally {
            this.isThinking = false;
        }
    }

    /**
     * 完成思考过程
     * @param {Object} move 选择的走法
     * @param {string} type 走法类型
     * @param {Object} searchResult 搜索结果
     * @returns {Object} 完整的走法信息
     */
    finishThinking(move, type, searchResult = null) {
        const thinkTime = Date.now() - this.thinkingStartTime;

        // 更新统计信息
        this.stats.movesPlayed++;
        this.stats.totalThinkTime += thinkTime;
        this.stats.averageThinkTime = this.stats.totalThinkTime / this.stats.movesPlayed;

        this.lastMove = {
            ...move,
            type,
            thinkTime,
            searchResult
        };

        return this.lastMove;
    }

    /**
     * 寻找必胜走法
     * @param {Array} board 棋盘状态
     * @param {string} color AI颜色
     * @returns {Object|null} 必胜走法
     */
    findWinningMove(board, color) {
        const rules = new ChessRules();
        const moves = this.searchEngine.generateMoves(board, color);

        for (const move of moves) {
            const newBoard = this.searchEngine.makeMove(board, move);
            const gameState = new GameState();
            gameState.board = newBoard;

            // 检查是否将死对方
            if (gameState.isCheckmate(color === 'red' ? 'black' : 'red')) {
                return move;
            }
        }

        return null;
    }

    /**
     * 寻找防守走法
     * @param {Array} board 棋盘状态
     * @param {string} color AI颜色
     * @returns {Object|null} 防守走法
     */
    findDefensiveMove(board, color) {
        const rules = new ChessRules();
        const gameState = new GameState();
        gameState.board = board;

        // 如果当前被将军，寻找解将走法
        if (gameState.isInCheck(color)) {
            const moves = this.searchEngine.generateMoves(board, color);

            for (const move of moves) {
                const newBoard = this.searchEngine.makeMove(board, move);
                const newGameState = new GameState();
                newGameState.board = newBoard;

                // 如果走法后不再被将军，返回这个走法
                if (!newGameState.isInCheck(color)) {
                    return move;
                }
            }
        }

        return null;
    }

    /**
     * 获取随机合法走法
     * @param {Array} board 棋盘状态
     * @param {string} color AI颜色
     * @returns {Object|null} 随机走法
     */
    getRandomMove(board, color) {
        const moves = this.searchEngine.generateMoves(board, color);

        if (moves.length === 0) {
            return null;
        }

        const randomIndex = Math.floor(Math.random() * moves.length);
        return moves[randomIndex];
    }

    /**
     * 获取提示
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {Object|null} 提示信息
     */
    async getHint(board, color) {
        try {
            // 使用较浅的搜索深度来快速获取提示
            const searchResult = this.searchEngine.searchBestMove(board, color, 2, 1000);

            if (searchResult && searchResult.move) {
                const move = searchResult.move;
                return {
                    move: move,
                    description: this.describeMoveHint(move, board),
                    score: searchResult.score,
                    confidence: this.calculateConfidence(searchResult)
                };
            }

            return this.hintSystem.getHint(board, color);
        } catch (error) {
            console.error('Error getting hint:', error);
            return null;
        }
    }

    /**
     * 描述走法提示
     * @param {Object} move 走法
     * @param {Array} board 棋盘状态
     * @returns {string} 走法描述
     */
    describeMoveHint(move, board) {
        const piece = move.piece;
        const pieceNames = {
            'king': '将',
            'advisor': '士',
            'elephant': '象',
            'horse': '马',
            'chariot': '车',
            'cannon': '炮',
            'pawn': '兵'
        };

        const pieceName = pieceNames[piece.type] || piece.type;
        const fromPos = `${String.fromCharCode(97 + move.from.col)}${10 - move.from.row}`;
        const toPos = `${String.fromCharCode(97 + move.to.col)}${10 - move.to.row}`;

        if (move.capturedPiece) {
            const capturedName = pieceNames[move.capturedPiece.type] || move.capturedPiece.type;
            return `${pieceName}${fromPos}吃${capturedName}${toPos}`;
        } else {
            return `${pieceName}${fromPos}到${toPos}`;
        }
    }

    /**
     * 计算提示的置信度
     * @param {Object} searchResult 搜索结果
     * @returns {number} 置信度 (0-1)
     */
    calculateConfidence(searchResult) {
        const stats = searchResult.stats;

        // 基于搜索节点数和时间计算置信度
        let confidence = 0.5;

        if (stats.nodesSearched > 1000) confidence += 0.2;
        if (stats.searchTime > 500) confidence += 0.1;
        if (Math.abs(searchResult.score) > 100) confidence += 0.2;

        return Math.min(confidence, 1.0);
    }

    /**
     * 获取AI统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            searchStats: this.searchEngine.getStats(),
            lastMove: this.lastMove
        };
    }

    /**
     * 重置AI状态
     */
    reset() {
        this.isThinking = false;
        this.lastMove = null;
        this.searchEngine.clearTranspositionTable();

        // 重置统计信息
        this.stats = {
            movesPlayed: 0,
            averageThinkTime: 0,
            totalThinkTime: 0,
            bestMoveAccuracy: 0
        };

        console.log('AI state reset');
    }

    /**
     * 销毁AI引擎
     */
    dispose() {
        this.reset();
        console.log('Chess AI disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChessAI;
} else {
    window.ChessAI = ChessAI;
}
