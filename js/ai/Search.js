/**
 * HuntGame - AI搜索引擎
 * 实现Alpha-Beta剪枝搜索算法
 */

class SearchEngine {
    constructor() {
        this.evaluation = new Evaluation();
        this.transpositionTable = new Map();
        this.maxDepth = 4;
        this.maxTime = 5000; // 5秒思考时间
        this.nodesSearched = 0;
        this.startTime = 0;
        
        // 搜索统计
        this.stats = {
            nodesSearched: 0,
            cutoffs: 0,
            transpositionHits: 0,
            searchTime: 0
        };
    }

    /**
     * 搜索最佳走法
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @param {number} depth 搜索深度
     * @param {number} maxTime 最大思考时间（毫秒）
     * @returns {Object} 最佳走法
     */
    searchBestMove(board, color, depth = this.maxDepth, maxTime = this.maxTime) {
        this.startTime = Date.now();
        this.maxDepth = depth;
        this.maxTime = maxTime;
        this.nodesSearched = 0;
        this.transpositionTable.clear();
        
        // 重置统计
        this.stats = {
            nodesSearched: 0,
            cutoffs: 0,
            transpositionHits: 0,
            searchTime: 0
        };
        
        let bestMove = null;
        let bestScore = -Infinity;
        
        // 获取所有可能的走法
        const moves = this.generateMoves(board, color);
        
        if (moves.length === 0) {
            return null;
        }
        
        // 走法排序（启发式搜索）
        this.orderMoves(moves, board, color);
        
        // 迭代加深搜索
        for (let currentDepth = 1; currentDepth <= depth; currentDepth++) {
            let iterationBestMove = null;
            let iterationBestScore = -Infinity;
            
            for (const move of moves) {
                // 检查时间限制
                if (Date.now() - this.startTime > maxTime) {
                    break;
                }
                
                // 执行走法
                const newBoard = this.makeMove(board, move);
                
                // Alpha-Beta搜索
                const score = -this.alphaBeta(
                    newBoard, 
                    color === 'red' ? 'black' : 'red',
                    currentDepth - 1,
                    -Infinity,
                    Infinity
                );
                
                if (score > iterationBestScore) {
                    iterationBestScore = score;
                    iterationBestMove = move;
                }
            }
            
            // 如果找到了更好的走法，更新最佳走法
            if (iterationBestMove && iterationBestScore > bestScore) {
                bestScore = iterationBestScore;
                bestMove = iterationBestMove;
            }
            
            // 如果时间不够，停止搜索
            if (Date.now() - this.startTime > maxTime * 0.8) {
                break;
            }
        }
        
        // 更新统计信息
        this.stats.searchTime = Date.now() - this.startTime;
        this.stats.nodesSearched = this.nodesSearched;
        
        return {
            move: bestMove,
            score: bestScore,
            stats: this.stats
        };
    }

    /**
     * Alpha-Beta剪枝搜索
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @param {number} depth 剩余搜索深度
     * @param {number} alpha Alpha值
     * @param {number} beta Beta值
     * @returns {number} 评估分数
     */
    alphaBeta(board, color, depth, alpha, beta) {
        this.nodesSearched++;
        
        // 检查时间限制
        if (Date.now() - this.startTime > this.maxTime) {
            return this.evaluation.quickEvaluate(board, color);
        }
        
        // 检查置换表
        const boardHash = this.hashBoard(board);
        const ttEntry = this.transpositionTable.get(boardHash);
        if (ttEntry && ttEntry.depth >= depth) {
            this.stats.transpositionHits++;
            return ttEntry.score;
        }
        
        // 叶子节点评估
        if (depth <= 0) {
            const score = this.evaluation.evaluate(board, color);
            this.transpositionTable.set(boardHash, { score, depth });
            return score;
        }
        
        // 生成走法
        const moves = this.generateMoves(board, color);
        
        // 如果没有走法，返回评估值
        if (moves.length === 0) {
            const score = this.evaluation.evaluate(board, color);
            return score;
        }
        
        // 走法排序
        this.orderMoves(moves, board, color);
        
        let bestScore = -Infinity;
        
        for (const move of moves) {
            // 执行走法
            const newBoard = this.makeMove(board, move);
            
            // 递归搜索
            const score = -this.alphaBeta(
                newBoard,
                color === 'red' ? 'black' : 'red',
                depth - 1,
                -beta,
                -alpha
            );
            
            bestScore = Math.max(bestScore, score);
            alpha = Math.max(alpha, score);
            
            // Beta剪枝
            if (beta <= alpha) {
                this.stats.cutoffs++;
                break;
            }
        }
        
        // 存储到置换表
        this.transpositionTable.set(boardHash, { score: bestScore, depth });
        
        return bestScore;
    }

    /**
     * 生成所有可能的走法
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     * @returns {Array} 走法列表
     */
    generateMoves(board, color) {
        const moves = [];
        const rules = new ChessRules();
        
        for (let fromRow = 0; fromRow < 10; fromRow++) {
            for (let fromCol = 0; fromCol < 9; fromCol++) {
                const piece = board[fromRow][fromCol];
                if (piece && piece.color === color) {
                    const possibleMoves = rules.getPossibleMoves(board, fromRow, fromCol);
                    
                    for (const [toRow, toCol] of possibleMoves) {
                        moves.push({
                            from: { row: fromRow, col: fromCol },
                            to: { row: toRow, col: toCol },
                            piece: piece,
                            capturedPiece: board[toRow][toCol]
                        });
                    }
                }
            }
        }
        
        return moves;
    }

    /**
     * 走法排序（启发式搜索优化）
     * @param {Array} moves 走法列表
     * @param {Array} board 棋盘状态
     * @param {string} color 当前玩家颜色
     */
    orderMoves(moves, board, color) {
        moves.sort((a, b) => {
            let scoreA = 0;
            let scoreB = 0;
            
            // 优先考虑吃子走法
            if (a.capturedPiece) {
                scoreA += this.evaluation.pieceValues[a.capturedPiece.type] || 0;
            }
            if (b.capturedPiece) {
                scoreB += this.evaluation.pieceValues[b.capturedPiece.type] || 0;
            }
            
            // 优先考虑中心走法
            const centerA = Math.abs(a.to.row - 4.5) + Math.abs(a.to.col - 4);
            const centerB = Math.abs(b.to.row - 4.5) + Math.abs(b.to.col - 4);
            scoreA -= centerA * 2;
            scoreB -= centerB * 2;
            
            return scoreB - scoreA;
        });
    }

    /**
     * 执行走法
     * @param {Array} board 原棋盘状态
     * @param {Object} move 走法
     * @returns {Array} 新棋盘状态
     */
    makeMove(board, move) {
        // 深拷贝棋盘
        const newBoard = board.map(row => row.slice());
        
        // 执行走法
        newBoard[move.to.row][move.to.col] = newBoard[move.from.row][move.from.col];
        newBoard[move.from.row][move.from.col] = null;
        
        return newBoard;
    }

    /**
     * 计算棋盘哈希值（简单实现）
     * @param {Array} board 棋盘状态
     * @returns {string} 哈希值
     */
    hashBoard(board) {
        let hash = '';
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece) {
                    hash += `${piece.type}${piece.color}${row}${col}`;
                } else {
                    hash += 'empty';
                }
            }
        }
        return hash;
    }

    /**
     * 设置搜索参数
     * @param {Object} params 参数对象
     */
    setParameters(params) {
        if (params.maxDepth !== undefined) {
            this.maxDepth = params.maxDepth;
        }
        if (params.maxTime !== undefined) {
            this.maxTime = params.maxTime;
        }
    }

    /**
     * 获取搜索统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * 清空置换表
     */
    clearTranspositionTable() {
        this.transpositionTable.clear();
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchEngine;
} else {
    window.SearchEngine = SearchEngine;
}
