/**
 * HuntGame - 本地存储工具
 * 提供游戏数据的本地存储和管理功能
 */

const StorageUtils = {
    /**
     * 获取完整的存储键名
     * @param {string} key 键名
     * @returns {string} 完整键名
     */
    getKey(key) {
        return GameConfig.storage.prefix + key;
    },

    /**
     * 检查浏览器是否支持localStorage
     * @returns {boolean} 是否支持
     */
    isSupported() {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    },

    /**
     * 存储数据
     * @param {string} key 键名
     * @param {*} value 要存储的值
     * @param {number} expireDays 过期天数（可选）
     * @returns {boolean} 是否存储成功
     */
    set(key, value, expireDays = null) {
        if (!this.isSupported()) {
            console.warn('LocalStorage not supported');
            return false;
        }

        try {
            const data = {
                value: value,
                timestamp: Date.now(),
                expireTime: expireDays ? Date.now() + (expireDays * 24 * 60 * 60 * 1000) : null
            };

            let jsonString = JSON.stringify(data);
            
            // 如果启用压缩，进行简单的压缩
            if (GameConfig.storage.compression) {
                jsonString = this.compress(jsonString);
            }

            localStorage.setItem(this.getKey(key), jsonString);
            return true;
        } catch (e) {
            console.error('Failed to save data to localStorage:', e);
            return false;
        }
    },

    /**
     * 获取数据
     * @param {string} key 键名
     * @param {*} defaultValue 默认值
     * @returns {*} 存储的值或默认值
     */
    get(key, defaultValue = null) {
        if (!this.isSupported()) {
            return defaultValue;
        }

        try {
            let jsonString = localStorage.getItem(this.getKey(key));
            
            if (!jsonString) {
                return defaultValue;
            }

            // 如果启用压缩，进行解压缩
            if (GameConfig.storage.compression) {
                jsonString = this.decompress(jsonString);
            }

            const data = JSON.parse(jsonString);
            
            // 检查是否过期
            if (data.expireTime && Date.now() > data.expireTime) {
                this.remove(key);
                return defaultValue;
            }

            return data.value;
        } catch (e) {
            console.error('Failed to load data from localStorage:', e);
            return defaultValue;
        }
    },

    /**
     * 删除数据
     * @param {string} key 键名
     * @returns {boolean} 是否删除成功
     */
    remove(key) {
        if (!this.isSupported()) {
            return false;
        }

        try {
            localStorage.removeItem(this.getKey(key));
            return true;
        } catch (e) {
            console.error('Failed to remove data from localStorage:', e);
            return false;
        }
    },

    /**
     * 清除所有游戏数据
     * @returns {boolean} 是否清除成功
     */
    clear() {
        if (!this.isSupported()) {
            return false;
        }

        try {
            const keys = Object.keys(localStorage);
            const prefix = GameConfig.storage.prefix;
            
            keys.forEach(key => {
                if (key.startsWith(prefix)) {
                    localStorage.removeItem(key);
                }
            });
            
            return true;
        } catch (e) {
            console.error('Failed to clear localStorage:', e);
            return false;
        }
    },

    /**
     * 获取存储使用情况
     * @returns {Object} 存储使用情况信息
     */
    getUsage() {
        if (!this.isSupported()) {
            return { used: 0, total: 0, available: 0 };
        }

        try {
            let used = 0;
            const prefix = GameConfig.storage.prefix;
            
            for (let key in localStorage) {
                if (key.startsWith(prefix)) {
                    used += localStorage[key].length;
                }
            }

            // 估算总可用空间（通常为5MB）
            const total = 5 * 1024 * 1024;
            const available = total - used;

            return {
                used: used,
                total: total,
                available: available,
                usedPercent: (used / total * 100).toFixed(2)
            };
        } catch (e) {
            console.error('Failed to get storage usage:', e);
            return { used: 0, total: 0, available: 0 };
        }
    },

    /**
     * 自动清理过期数据
     * @returns {number} 清理的项目数量
     */
    cleanup() {
        if (!this.isSupported() || !GameConfig.storage.autoCleanup) {
            return 0;
        }

        let cleanedCount = 0;
        const prefix = GameConfig.storage.prefix;
        const keys = Object.keys(localStorage);

        keys.forEach(key => {
            if (key.startsWith(prefix)) {
                try {
                    let jsonString = localStorage.getItem(key);
                    
                    if (GameConfig.storage.compression) {
                        jsonString = this.decompress(jsonString);
                    }

                    const data = JSON.parse(jsonString);
                    
                    if (data.expireTime && Date.now() > data.expireTime) {
                        localStorage.removeItem(key);
                        cleanedCount++;
                    }
                } catch (e) {
                    // 如果数据损坏，也删除它
                    localStorage.removeItem(key);
                    cleanedCount++;
                }
            }
        });

        if (cleanedCount > 0) {
            console.log(`Cleaned up ${cleanedCount} expired storage items`);
        }

        return cleanedCount;
    },

    /**
     * 简单的字符串压缩
     * @param {string} str 要压缩的字符串
     * @returns {string} 压缩后的字符串
     */
    compress(str) {
        // 这里使用简单的RLE压缩算法
        // 在实际项目中可以使用更高效的压缩算法
        return str.replace(/(.)\1+/g, (match, char) => {
            return char + match.length;
        });
    },

    /**
     * 解压缩字符串
     * @param {string} str 压缩的字符串
     * @returns {string} 解压缩后的字符串
     */
    decompress(str) {
        // 对应的解压缩算法
        return str.replace(/(.)\d+/g, (match, char) => {
            const count = parseInt(match.slice(1));
            return char.repeat(count);
        });
    },

    /**
     * 导出所有游戏数据
     * @returns {Object} 导出的数据对象
     */
    exportData() {
        if (!this.isSupported()) {
            return null;
        }

        const exportData = {};
        const prefix = GameConfig.storage.prefix;
        const keys = Object.keys(localStorage);

        keys.forEach(key => {
            if (key.startsWith(prefix)) {
                const shortKey = key.replace(prefix, '');
                exportData[shortKey] = this.get(shortKey);
            }
        });

        return {
            version: GameConfig.version,
            exportTime: Date.now(),
            data: exportData
        };
    },

    /**
     * 导入游戏数据
     * @param {Object} importData 要导入的数据
     * @returns {boolean} 是否导入成功
     */
    importData(importData) {
        if (!this.isSupported() || !importData || !importData.data) {
            return false;
        }

        try {
            // 检查版本兼容性
            if (importData.version !== GameConfig.version) {
                console.warn('Import data version mismatch');
            }

            // 导入数据
            for (const key in importData.data) {
                this.set(key, importData.data[key]);
            }

            return true;
        } catch (e) {
            console.error('Failed to import data:', e);
            return false;
        }
    },

    // 快捷方法：游戏状态相关
    saveGameState(gameState) {
        return this.set(GameConfig.storage.keys.gameState, gameState);
    },

    loadGameState() {
        return this.get(GameConfig.storage.keys.gameState);
    },

    // 快捷方法：设置相关
    saveSettings(settings) {
        return this.set(GameConfig.storage.keys.settings, settings);
    },

    loadSettings() {
        return this.get(GameConfig.storage.keys.settings, {
            difficulty: GameConfig.game.defaultDifficulty,
            enableHints: GameConfig.game.enableHints,
            enableSound: GameConfig.game.enableSound,
            enableAnimation: GameConfig.game.enableAnimation
        });
    },

    // 快捷方法：历史记录相关
    saveHistory(history) {
        return this.set(GameConfig.storage.keys.history, history);
    },

    loadHistory() {
        return this.get(GameConfig.storage.keys.history, []);
    },

    // 快捷方法：统计数据相关
    saveStatistics(stats) {
        return this.set(GameConfig.storage.keys.statistics, stats);
    },

    loadStatistics() {
        return this.get(GameConfig.storage.keys.statistics, {
            gamesPlayed: 0,
            gamesWon: 0,
            gamesLost: 0,
            totalPlayTime: 0,
            bestWinTime: null,
            averageMoveTime: 0
        });
    }
};

// 页面加载时自动清理过期数据
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        StorageUtils.cleanup();
    });
}

// 导出工具对象
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageUtils;
} else {
    window.StorageUtils = StorageUtils;
}
