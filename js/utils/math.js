/**
 * HuntGame - 数学工具函数
 * 提供游戏中需要的各种数学计算功能
 */

const MathUtils = {
    /**
     * 将角度转换为弧度
     * @param {number} degrees 角度值
     * @returns {number} 弧度值
     */
    degToRad(degrees) {
        return degrees * Math.PI / 180;
    },

    /**
     * 将弧度转换为角度
     * @param {number} radians 弧度值
     * @returns {number} 角度值
     */
    radToDeg(radians) {
        return radians * 180 / Math.PI;
    },

    /**
     * 限制数值在指定范围内
     * @param {number} value 要限制的值
     * @param {number} min 最小值
     * @param {number} max 最大值
     * @returns {number} 限制后的值
     */
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    /**
     * 线性插值
     * @param {number} start 起始值
     * @param {number} end 结束值
     * @param {number} t 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    lerp(start, end, t) {
        return start + (end - start) * t;
    },

    /**
     * 平滑插值（使用缓动函数）
     * @param {number} start 起始值
     * @param {number} end 结束值
     * @param {number} t 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    smoothLerp(start, end, t) {
        // 使用缓入缓出函数
        const smoothT = t * t * (3 - 2 * t);
        return this.lerp(start, end, smoothT);
    },

    /**
     * 计算两点之间的距离
     * @param {number} x1 第一个点的x坐标
     * @param {number} y1 第一个点的y坐标
     * @param {number} x2 第二个点的x坐标
     * @param {number} y2 第二个点的y坐标
     * @returns {number} 距离
     */
    distance2D(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    },

    /**
     * 计算3D空间中两点之间的距离
     * @param {Object} point1 第一个点 {x, y, z}
     * @param {Object} point2 第二个点 {x, y, z}
     * @returns {number} 距离
     */
    distance3D(point1, point2) {
        const dx = point2.x - point1.x;
        const dy = point2.y - point1.y;
        const dz = point2.z - point1.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    },

    /**
     * 将棋盘坐标转换为3D世界坐标
     * @param {number} row 行索引 (0-8)
     * @param {number} col 列索引 (0-9)
     * @returns {Object} 3D坐标 {x, y, z}
     */
    boardToWorld(row, col) {
        const cellSize = GameConfig.board.cellSize;
        const boardWidth = GameConfig.board.size.width;
        const boardHeight = GameConfig.board.size.height;
        
        return {
            x: (col - boardWidth / 2) * cellSize,
            y: 0,
            z: (row - boardHeight / 2) * cellSize
        };
    },

    /**
     * 将3D世界坐标转换为棋盘坐标
     * @param {number} x 世界坐标x
     * @param {number} z 世界坐标z
     * @returns {Object} 棋盘坐标 {row, col}
     */
    worldToBoard(x, z) {
        const cellSize = GameConfig.board.cellSize;
        const boardWidth = GameConfig.board.size.width;
        const boardHeight = GameConfig.board.size.height;
        
        const col = Math.round(x / cellSize + boardWidth / 2);
        const row = Math.round(z / cellSize + boardHeight / 2);
        
        return { row, col };
    },

    /**
     * 检查棋盘坐标是否有效
     * @param {number} row 行索引
     * @param {number} col 列索引
     * @returns {boolean} 是否有效
     */
    isValidBoardPosition(row, col) {
        return row >= 0 && row < 10 && col >= 0 && col < 9;
    },

    /**
     * 生成随机数
     * @param {number} min 最小值
     * @param {number} max 最大值
     * @returns {number} 随机数
     */
    random(min = 0, max = 1) {
        return Math.random() * (max - min) + min;
    },

    /**
     * 生成随机整数
     * @param {number} min 最小值
     * @param {number} max 最大值
     * @returns {number} 随机整数
     */
    randomInt(min, max) {
        return Math.floor(this.random(min, max + 1));
    },

    /**
     * 从数组中随机选择一个元素
     * @param {Array} array 数组
     * @returns {*} 随机选择的元素
     */
    randomChoice(array) {
        return array[this.randomInt(0, array.length - 1)];
    },

    /**
     * 打乱数组顺序
     * @param {Array} array 要打乱的数组
     * @returns {Array} 打乱后的新数组
     */
    shuffle(array) {
        const result = [...array];
        for (let i = result.length - 1; i > 0; i--) {
            const j = this.randomInt(0, i);
            [result[i], result[j]] = [result[j], result[i]];
        }
        return result;
    },

    /**
     * 缓动函数 - 缓入
     * @param {number} t 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeIn(t) {
        return t * t;
    },

    /**
     * 缓动函数 - 缓出
     * @param {number} t 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeOut(t) {
        return 1 - (1 - t) * (1 - t);
    },

    /**
     * 缓动函数 - 缓入缓出
     * @param {number} t 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeInOut(t) {
        return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
    },

    /**
     * 弹性缓动函数
     * @param {number} t 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeElastic(t) {
        if (t === 0 || t === 1) return t;
        const p = 0.3;
        const s = p / 4;
        return Math.pow(2, -10 * t) * Math.sin((t - s) * (2 * Math.PI) / p) + 1;
    },

    /**
     * 回弹缓动函数
     * @param {number} t 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeBounce(t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    },

    /**
     * 将数值格式化为指定小数位数
     * @param {number} value 数值
     * @param {number} decimals 小数位数
     * @returns {number} 格式化后的数值
     */
    round(value, decimals = 0) {
        const factor = Math.pow(10, decimals);
        return Math.round(value * factor) / factor;
    },

    /**
     * 检查两个浮点数是否近似相等
     * @param {number} a 第一个数
     * @param {number} b 第二个数
     * @param {number} epsilon 误差范围
     * @returns {boolean} 是否近似相等
     */
    approximately(a, b, epsilon = 0.0001) {
        return Math.abs(a - b) < epsilon;
    }
};

// 导出工具对象
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MathUtils;
} else {
    window.MathUtils = MathUtils;
}
