/**
 * HuntGame - 提示面板
 * 显示AI分析和提示信息的UI组件
 */

class HintPanel {
    constructor() {
        this.isVisible = false;
        this.currentHint = null;
        this.animationDuration = 300;
        
        this.createPanel();
        this.bindEvents();
        
        console.log('Hint panel initialized');
    }

    /**
     * 创建提示面板
     */
    createPanel() {
        // 创建主容器
        this.panel = document.createElement('div');
        this.panel.className = 'hint-panel';
        this.panel.innerHTML = `
            <div class="hint-header">
                <h3 class="hint-title">
                    <i class="icon-lightbulb"></i>
                    AI分析
                </h3>
                <button class="hint-close-btn" title="关闭">
                    <i class="icon-close"></i>
                </button>
            </div>
            
            <div class="hint-content">
                <div class="hint-loading">
                    <div class="loading-spinner"></div>
                    <span>分析中...</span>
                </div>
                
                <div class="hint-analysis" style="display: none;">
                    <div class="position-evaluation">
                        <div class="eval-score">
                            <span class="eval-label">局面评估：</span>
                            <span class="eval-value">均衡</span>
                        </div>
                        <div class="eval-bar">
                            <div class="eval-progress"></div>
                        </div>
                    </div>
                    
                    <div class="main-hint">
                        <div class="hint-description"></div>
                        <div class="hint-confidence">
                            <span class="confidence-label">置信度：</span>
                            <div class="confidence-bar">
                                <div class="confidence-fill"></div>
                            </div>
                            <span class="confidence-value">0%</span>
                        </div>
                    </div>
                    
                    <div class="hint-details">
                        <div class="hint-tabs">
                            <button class="hint-tab active" data-tab="moves">推荐走法</button>
                            <button class="hint-tab" data-tab="analysis">局面分析</button>
                            <button class="hint-tab" data-tab="threats">威胁分析</button>
                        </div>
                        
                        <div class="hint-tab-content">
                            <div class="tab-panel active" data-panel="moves">
                                <div class="recommended-moves"></div>
                            </div>
                            
                            <div class="tab-panel" data-panel="analysis">
                                <div class="position-analysis">
                                    <div class="analysis-item">
                                        <span class="analysis-label">游戏阶段：</span>
                                        <span class="analysis-value phase-value">开局</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="analysis-label">材质平衡：</span>
                                        <span class="analysis-value material-value">均衡</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="analysis-label">王的安全：</span>
                                        <span class="analysis-value safety-value">安全</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="analysis-label">位置因素：</span>
                                        <span class="analysis-value position-value">良好</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="tab-panel" data-panel="threats">
                                <div class="threats-list"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="hint-error" style="display: none;">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">分析失败，请重试</div>
                    <button class="retry-btn">重新分析</button>
                </div>
            </div>
            
            <div class="hint-actions">
                <button class="hint-btn hint-btn-primary" id="apply-hint">
                    <i class="icon-check"></i>
                    应用建议
                </button>
                <button class="hint-btn hint-btn-secondary" id="refresh-hint">
                    <i class="icon-refresh"></i>
                    重新分析
                </button>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(this.panel);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭按钮
        const closeBtn = this.panel.querySelector('.hint-close-btn');
        closeBtn.addEventListener('click', () => this.hide());
        
        // 标签切换
        const tabs = this.panel.querySelectorAll('.hint-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // 应用建议按钮
        const applyBtn = this.panel.querySelector('#apply-hint');
        applyBtn.addEventListener('click', () => this.applyHint());
        
        // 重新分析按钮
        const refreshBtn = this.panel.querySelector('#refresh-hint');
        refreshBtn.addEventListener('click', () => this.refreshHint());
        
        // 重试按钮
        const retryBtn = this.panel.querySelector('.retry-btn');
        retryBtn.addEventListener('click', () => this.refreshHint());
        
        // 点击外部关闭
        document.addEventListener('click', (e) => {
            if (this.isVisible && !this.panel.contains(e.target)) {
                const hintButton = document.querySelector('.hint-button');
                if (!hintButton || !hintButton.contains(e.target)) {
                    this.hide();
                }
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });
    }

    /**
     * 显示提示面板
     * @param {Object} hintData 提示数据
     */
    show(hintData = null) {
        this.isVisible = true;
        this.panel.classList.add('visible');
        
        if (hintData) {
            this.displayHint(hintData);
        } else {
            this.showLoading();
        }
        
        // 添加显示动画
        this.panel.style.transform = 'translateX(100%)';
        this.panel.style.opacity = '0';
        
        requestAnimationFrame(() => {
            this.panel.style.transition = `all ${this.animationDuration}ms ease-out`;
            this.panel.style.transform = 'translateX(0)';
            this.panel.style.opacity = '1';
        });
    }

    /**
     * 隐藏提示面板
     */
    hide() {
        if (!this.isVisible) return;
        
        this.panel.style.transition = `all ${this.animationDuration}ms ease-in`;
        this.panel.style.transform = 'translateX(100%)';
        this.panel.style.opacity = '0';
        
        setTimeout(() => {
            this.isVisible = false;
            this.panel.classList.remove('visible');
            this.panel.style.transition = '';
            this.panel.style.transform = '';
            this.panel.style.opacity = '';
        }, this.animationDuration);
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        this.hideAllStates();
        this.panel.querySelector('.hint-loading').style.display = 'flex';
    }

    /**
     * 显示错误状态
     * @param {string} message 错误消息
     */
    showError(message = '分析失败，请重试') {
        this.hideAllStates();
        const errorPanel = this.panel.querySelector('.hint-error');
        errorPanel.querySelector('.error-message').textContent = message;
        errorPanel.style.display = 'block';
    }

    /**
     * 显示提示内容
     * @param {Object} hintData 提示数据
     */
    displayHint(hintData) {
        this.currentHint = hintData;
        this.hideAllStates();
        
        const analysisPanel = this.panel.querySelector('.hint-analysis');
        analysisPanel.style.display = 'block';
        
        // 更新局面评估
        this.updatePositionEvaluation(hintData.analysis);
        
        // 更新主要提示
        this.updateMainHint(hintData);
        
        // 更新详细信息
        this.updateRecommendedMoves(hintData.hints || []);
        this.updatePositionAnalysis(hintData.analysis);
        this.updateThreatsAnalysis(hintData.analysis.threats || []);
    }

    /**
     * 隐藏所有状态面板
     */
    hideAllStates() {
        this.panel.querySelector('.hint-loading').style.display = 'none';
        this.panel.querySelector('.hint-analysis').style.display = 'none';
        this.panel.querySelector('.hint-error').style.display = 'none';
    }

    /**
     * 更新局面评估
     * @param {Object} analysis 分析数据
     */
    updatePositionEvaluation(analysis) {
        if (!analysis) return;
        
        const evalValue = this.panel.querySelector('.eval-value');
        const evalProgress = this.panel.querySelector('.eval-progress');
        
        evalValue.textContent = analysis.evaluation || '均衡';
        
        // 更新评估条
        const score = analysis.score || 0;
        const percentage = Math.max(0, Math.min(100, 50 + score / 4));
        evalProgress.style.width = percentage + '%';
        
        // 设置颜色
        if (score > 50) {
            evalProgress.className = 'eval-progress advantage';
        } else if (score < -50) {
            evalProgress.className = 'eval-progress disadvantage';
        } else {
            evalProgress.className = 'eval-progress balanced';
        }
    }

    /**
     * 更新主要提示
     * @param {Object} hintData 提示数据
     */
    updateMainHint(hintData) {
        const description = this.panel.querySelector('.hint-description');
        const confidenceValue = this.panel.querySelector('.confidence-value');
        const confidenceFill = this.panel.querySelector('.confidence-fill');
        
        description.textContent = hintData.description || '暂无建议';
        
        const confidence = (hintData.confidence || 0) * 100;
        confidenceValue.textContent = Math.round(confidence) + '%';
        confidenceFill.style.width = confidence + '%';
        
        // 设置置信度颜色
        if (confidence > 80) {
            confidenceFill.className = 'confidence-fill high';
        } else if (confidence > 60) {
            confidenceFill.className = 'confidence-fill medium';
        } else {
            confidenceFill.className = 'confidence-fill low';
        }
    }

    /**
     * 更新推荐走法
     * @param {Array} hints 提示列表
     */
    updateRecommendedMoves(hints) {
        const container = this.panel.querySelector('.recommended-moves');
        container.innerHTML = '';
        
        if (hints.length === 0) {
            container.innerHTML = '<div class="no-hints">暂无推荐走法</div>';
            return;
        }
        
        hints.forEach((hint, index) => {
            const moveElement = document.createElement('div');
            moveElement.className = 'recommended-move';
            moveElement.innerHTML = `
                <div class="move-rank">${index + 1}</div>
                <div class="move-info">
                    <div class="move-description">${hint.description}</div>
                    <div class="move-reasoning">${hint.reasoning}</div>
                </div>
                <div class="move-score">+${Math.round(hint.score)}</div>
            `;
            
            // 点击应用走法
            moveElement.addEventListener('click', () => {
                this.applySpecificHint(hint);
            });
            
            container.appendChild(moveElement);
        });
    }

    /**
     * 更新位置分析
     * @param {Object} analysis 分析数据
     */
    updatePositionAnalysis(analysis) {
        if (!analysis) return;
        
        const phaseValue = this.panel.querySelector('.phase-value');
        const materialValue = this.panel.querySelector('.material-value');
        const safetyValue = this.panel.querySelector('.safety-value');
        const positionValue = this.panel.querySelector('.position-value');
        
        phaseValue.textContent = this.getPhaseText(analysis.phase);
        materialValue.textContent = this.getMaterialText(analysis.materialBalance);
        safetyValue.textContent = this.getSafetyText(analysis.kingSafety);
        positionValue.textContent = this.getPositionText(analysis.positionalFactors);
    }

    /**
     * 更新威胁分析
     * @param {Array} threats 威胁列表
     */
    updateThreatsAnalysis(threats) {
        const container = this.panel.querySelector('.threats-list');
        container.innerHTML = '';
        
        if (threats.length === 0) {
            container.innerHTML = '<div class="no-threats">当前没有明显威胁</div>';
            return;
        }
        
        threats.forEach(threat => {
            const threatElement = document.createElement('div');
            threatElement.className = 'threat-item';
            threatElement.innerHTML = `
                <div class="threat-severity severity-${this.getSeverityLevel(threat.severity)}">
                    ${this.getSeverityIcon(threat.severity)}
                </div>
                <div class="threat-info">
                    <div class="threat-description">
                        ${threat.target.piece.type}受到${threat.attacker.piece.type}威胁
                    </div>
                    <div class="threat-position">
                        位置：${String.fromCharCode(97 + threat.target.col)}${10 - threat.target.row}
                    </div>
                </div>
            `;
            
            container.appendChild(threatElement);
        });
    }

    /**
     * 切换标签
     * @param {string} tabName 标签名
     */
    switchTab(tabName) {
        // 更新标签状态
        const tabs = this.panel.querySelectorAll('.hint-tab');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // 更新面板状态
        const panels = this.panel.querySelectorAll('.tab-panel');
        panels.forEach(panel => {
            panel.classList.toggle('active', panel.dataset.panel === tabName);
        });
    }

    /**
     * 应用提示
     */
    applyHint() {
        if (this.currentHint && this.currentHint.move) {
            this.onApplyHint?.(this.currentHint.move);
            this.hide();
        }
    }

    /**
     * 应用特定提示
     * @param {Object} hint 特定提示
     */
    applySpecificHint(hint) {
        if (hint && hint.move) {
            this.onApplyHint?.(hint.move);
            this.hide();
        }
    }

    /**
     * 刷新提示
     */
    refreshHint() {
        this.showLoading();
        this.onRefreshHint?.();
    }

    /**
     * 设置回调函数
     * @param {Object} callbacks 回调函数
     */
    setCallbacks(callbacks) {
        this.onApplyHint = callbacks.onApplyHint;
        this.onRefreshHint = callbacks.onRefreshHint;
    }

    // 辅助方法
    getPhaseText(phase) {
        const phases = {
            'opening': '开局',
            'middlegame': '中局',
            'endgame': '残局'
        };
        return phases[phase] || '未知';
    }

    getMaterialText(material) {
        if (!material) return '均衡';
        
        if (material.balance > 50) return '优势';
        if (material.balance < -50) return '劣势';
        return '均衡';
    }

    getSafetyText(safety) {
        if (safety > 20) return '安全';
        if (safety > 0) return '一般';
        return '危险';
    }

    getPositionText(factors) {
        if (!factors) return '一般';
        
        const total = factors.centerControl + factors.development + factors.coordination;
        if (total > 50) return '优秀';
        if (total > 20) return '良好';
        return '一般';
    }

    getSeverityLevel(severity) {
        if (severity > 80) return 'high';
        if (severity > 40) return 'medium';
        return 'low';
    }

    getSeverityIcon(severity) {
        if (severity > 80) return '🔴';
        if (severity > 40) return '🟡';
        return '🟢';
    }

    /**
     * 检查是否可见
     * @returns {boolean} 是否可见
     */
    isShowing() {
        return this.isVisible;
    }

    /**
     * 销毁面板
     */
    dispose() {
        if (this.panel && this.panel.parentNode) {
            this.panel.parentNode.removeChild(this.panel);
        }
        
        this.currentHint = null;
        this.onApplyHint = null;
        this.onRefreshHint = null;
        
        console.log('Hint panel disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HintPanel;
} else {
    window.HintPanel = HintPanel;
}
