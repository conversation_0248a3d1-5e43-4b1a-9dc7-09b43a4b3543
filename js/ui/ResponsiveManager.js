/**
 * HuntGame - 响应式管理器
 * 完整的响应式和移动端优化管理系统
 */

class ResponsiveManager {
    constructor(canvas, camera, renderer) {
        this.canvas = canvas;
        this.camera = camera;
        this.renderer = renderer;

        // 断点配置
        this.breakpoints = {
            mobile: 768,
            tablet: 1024,
            desktop: Infinity
        };

        // 当前状态
        this.currentBreakpoint = 'desktop';
        this.orientation = 'landscape';
        this.pixelRatio = window.devicePixelRatio || 1;

        // 设备检测
        this.deviceInfo = this.detectDevice();

        // 性能配置
        this.performanceSettings = {
            mobile: {
                shadowMapSize: 512,
                antialias: false,
                maxLights: 2,
                renderScale: 0.8
            },
            tablet: {
                shadowMapSize: 1024,
                antialias: true,
                maxLights: 3,
                renderScale: 0.9
            },
            desktop: {
                shadowMapSize: 2048,
                antialias: true,
                maxLights: 4,
                renderScale: 1.0
            }
        };

        // 回调函数
        this.callbacks = {
            onBreakpointChange: null,
            onOrientationChange: null,
            onPerformanceChange: null
        };

        // 事件监听器引用
        this.eventListeners = [];

        this.init();
    }

    /**
     * 初始化响应式管理器
     */
    init() {
        this.detectInitialState();
        this.bindEvents();
        this.applyResponsiveSettings();

        console.log('Responsive manager initialized', {
            breakpoint: this.currentBreakpoint,
            orientation: this.orientation,
            device: this.deviceInfo.type,
            pixelRatio: this.pixelRatio
        });
    }

    /**
     * 检测设备信息
     * @returns {Object} 设备信息
     */
    detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const platform = navigator.platform.toLowerCase();

        const device = {
            type: 'desktop',
            os: 'unknown',
            browser: 'unknown',
            isTouchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
            isRetina: window.devicePixelRatio > 1,
            hasGyroscope: 'DeviceOrientationEvent' in window,
            hasAccelerometer: 'DeviceMotionEvent' in window
        };

        // 检测操作系统
        if (userAgent.includes('android')) {
            device.os = 'android';
            device.type = 'mobile';
        } else if (userAgent.includes('iphone') || userAgent.includes('ipod')) {
            device.os = 'ios';
            device.type = 'mobile';
        } else if (userAgent.includes('ipad')) {
            device.os = 'ios';
            device.type = 'tablet';
        } else if (userAgent.includes('mac')) {
            device.os = 'macos';
        } else if (userAgent.includes('win')) {
            device.os = 'windows';
        } else if (userAgent.includes('linux')) {
            device.os = 'linux';
        }

        // 检测浏览器
        if (userAgent.includes('chrome')) {
            device.browser = 'chrome';
        } else if (userAgent.includes('firefox')) {
            device.browser = 'firefox';
        } else if (userAgent.includes('safari')) {
            device.browser = 'safari';
        } else if (userAgent.includes('edge')) {
            device.browser = 'edge';
        }

        return device;
    }

    /**
     * 检测初始状态
     */
    detectInitialState() {
        this.updateBreakpoint();
        this.updateOrientation();
        this.updatePixelRatio();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        const resizeHandler = this.debounce(() => {
            this.handleResize();
        }, 100);

        const orientationHandler = () => {
            // 延迟处理方向变化，等待浏览器完成布局
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        };

        this.addEventListeners([
            { element: window, event: 'resize', handler: resizeHandler },
            { element: window, event: 'orientationchange', handler: orientationHandler },
            { element: screen, event: 'orientationchange', handler: orientationHandler }
        ]);

        // 监听设备像素比变化（用户缩放）
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia(`(resolution: ${this.pixelRatio}dppx)`);
            const pixelRatioHandler = () => {
                this.handlePixelRatioChange();
            };

            if (mediaQuery.addEventListener) {
                mediaQuery.addEventListener('change', pixelRatioHandler);
            } else {
                mediaQuery.addListener(pixelRatioHandler);
            }

            this.eventListeners.push({
                element: mediaQuery,
                event: 'change',
                handler: pixelRatioHandler
            });
        }
    }

    /**
     * 添加事件监听器
     * @param {Array} listeners 监听器配置数组
     */
    addEventListeners(listeners) {
        listeners.forEach(({ element, event, handler }) => {
            if (element && element.addEventListener) {
                element.addEventListener(event, handler);
                this.eventListeners.push({ element, event, handler });
            }
        });
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        const oldBreakpoint = this.currentBreakpoint;

        this.updateBreakpoint();
        this.updateOrientation();
        this.resizeRenderer();

        if (oldBreakpoint !== this.currentBreakpoint) {
            this.applyResponsiveSettings();
        }
    }

    /**
     * 处理方向变化
     */
    handleOrientationChange() {
        const oldOrientation = this.orientation;

        this.updateOrientation();
        this.resizeRenderer();

        if (oldOrientation !== this.orientation) {
            this.onOrientationChange(this.orientation);
        }
    }

    /**
     * 处理像素比变化
     */
    handlePixelRatioChange() {
        const oldPixelRatio = this.pixelRatio;
        this.updatePixelRatio();

        if (oldPixelRatio !== this.pixelRatio) {
            this.resizeRenderer();
            console.log(`Pixel ratio changed to: ${this.pixelRatio}`);
        }
    }

    /**
     * 更新断点
     */
    updateBreakpoint() {
        const width = window.innerWidth;
        let newBreakpoint = 'desktop';

        if (width <= this.breakpoints.mobile) {
            newBreakpoint = 'mobile';
        } else if (width <= this.breakpoints.tablet) {
            newBreakpoint = 'tablet';
        }

        if (newBreakpoint !== this.currentBreakpoint) {
            const oldBreakpoint = this.currentBreakpoint;
            this.currentBreakpoint = newBreakpoint;
            this.onBreakpointChange(newBreakpoint, oldBreakpoint);
        }
    }

    /**
     * 更新方向
     */
    updateOrientation() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        const newOrientation = width > height ? 'landscape' : 'portrait';

        if (newOrientation !== this.orientation) {
            this.orientation = newOrientation;
        }
    }

    /**
     * 更新像素比
     */
    updatePixelRatio() {
        this.pixelRatio = window.devicePixelRatio || 1;
    }

    /**
     * 调整渲染器大小
     */
    resizeRenderer() {
        if (!this.renderer || !this.camera || !this.canvas) return;

        const width = this.canvas.clientWidth;
        const height = this.canvas.clientHeight;
        const pixelRatio = Math.min(this.pixelRatio, 2); // 限制最大像素比

        // 获取性能设置
        const perfSettings = this.performanceSettings[this.currentBreakpoint];
        const renderScale = perfSettings.renderScale;

        // 计算实际渲染尺寸
        const renderWidth = Math.floor(width * renderScale);
        const renderHeight = Math.floor(height * renderScale);

        // 更新渲染器
        this.renderer.setSize(renderWidth, renderHeight);
        this.renderer.setPixelRatio(pixelRatio);

        // 更新相机
        if (this.camera.isPerspectiveCamera) {
            this.camera.aspect = width / height;
            this.camera.updateProjectionMatrix();
        }

        // 更新canvas样式
        this.canvas.style.width = width + 'px';
        this.canvas.style.height = height + 'px';

        console.log(`Renderer resized: ${renderWidth}x${renderHeight} (scale: ${renderScale})`);
    }

    /**
     * 应用响应式设置
     */
    applyResponsiveSettings() {
        const perfSettings = this.performanceSettings[this.currentBreakpoint];

        // 应用渲染设置
        if (this.renderer) {
            // 阴影设置
            if (this.renderer.shadowMap) {
                this.renderer.shadowMap.mapSize.width = perfSettings.shadowMapSize;
                this.renderer.shadowMap.mapSize.height = perfSettings.shadowMapSize;
            }

            // 抗锯齿设置
            if (this.renderer.getContext) {
                const context = this.renderer.getContext();
                if (context && context.getContextAttributes) {
                    const attributes = context.getContextAttributes();
                    if (attributes.antialias !== perfSettings.antialias) {
                        console.log(`Antialias setting: ${perfSettings.antialias}`);
                    }
                }
            }
        }

        // 应用CSS类
        document.body.className = `breakpoint-${this.currentBreakpoint} orientation-${this.orientation}`;

        // 添加设备类
        if (this.deviceInfo.isTouchDevice) {
            document.body.classList.add('touch-device');
        }

        if (this.deviceInfo.isRetina) {
            document.body.classList.add('retina-device');
        }

        // 触发性能变化回调
        if (this.callbacks.onPerformanceChange) {
            this.callbacks.onPerformanceChange(perfSettings, this.currentBreakpoint);
        }

        console.log(`Applied responsive settings for ${this.currentBreakpoint}:`, perfSettings);
    }

    /**
     * 断点变化处理
     * @param {string} newBreakpoint 新断点
     * @param {string} oldBreakpoint 旧断点
     */
    onBreakpointChange(newBreakpoint, oldBreakpoint) {
        console.log(`Breakpoint changed from ${oldBreakpoint} to ${newBreakpoint}`);

        // 触发回调
        if (this.callbacks.onBreakpointChange) {
            this.callbacks.onBreakpointChange(newBreakpoint, oldBreakpoint);
        }

        // 应用新设置
        this.applyResponsiveSettings();
    }

    /**
     * 方向变化处理
     * @param {string} orientation 新方向
     */
    onOrientationChange(orientation) {
        console.log(`Orientation changed to: ${orientation}`);

        // 更新CSS类
        document.body.classList.remove('orientation-landscape', 'orientation-portrait');
        document.body.classList.add(`orientation-${orientation}`);

        // 触发回调
        if (this.callbacks.onOrientationChange) {
            this.callbacks.onOrientationChange(orientation);
        }

        // 移动设备方向变化时的特殊处理
        if (this.isMobile()) {
            this.handleMobileOrientationChange(orientation);
        }
    }

    /**
     * 处理移动设备方向变化
     * @param {string} orientation 方向
     */
    handleMobileOrientationChange(orientation) {
        // 隐藏地址栏（iOS Safari）
        if (this.deviceInfo.os === 'ios') {
            setTimeout(() => {
                window.scrollTo(0, 1);
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 100);
            }, 500);
        }

        // 调整UI元素
        const hudElements = document.querySelectorAll('.hud-element');
        hudElements.forEach(element => {
            if (orientation === 'portrait') {
                element.classList.add('portrait-mode');
                element.classList.remove('landscape-mode');
            } else {
                element.classList.add('landscape-mode');
                element.classList.remove('portrait-mode');
            }
        });
    }

    /**
     * 设置回调函数
     * @param {Object} callbacks 回调函数对象
     */
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * 获取当前断点
     * @returns {string} 当前断点
     */
    getCurrentBreakpoint() {
        return this.currentBreakpoint;
    }

    /**
     * 获取当前方向
     * @returns {string} 当前方向
     */
    getCurrentOrientation() {
        return this.orientation;
    }

    /**
     * 获取设备信息
     * @returns {Object} 设备信息
     */
    getDeviceInfo() {
        return { ...this.deviceInfo };
    }

    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    isMobile() {
        return this.currentBreakpoint === 'mobile';
    }

    /**
     * 检查是否为平板设备
     * @returns {boolean} 是否为平板设备
     */
    isTablet() {
        return this.currentBreakpoint === 'tablet';
    }

    /**
     * 检查是否为桌面设备
     * @returns {boolean} 是否为桌面设备
     */
    isDesktop() {
        return this.currentBreakpoint === 'desktop';
    }

    /**
     * 检查是否为触摸设备
     * @returns {boolean} 是否为触摸设备
     */
    isTouchDevice() {
        return this.deviceInfo.isTouchDevice;
    }

    /**
     * 检查是否为高分辨率设备
     * @returns {boolean} 是否为高分辨率设备
     */
    isRetina() {
        return this.deviceInfo.isRetina;
    }

    /**
     * 检查是否为竖屏
     * @returns {boolean} 是否为竖屏
     */
    isPortrait() {
        return this.orientation === 'portrait';
    }

    /**
     * 检查是否为横屏
     * @returns {boolean} 是否为横屏
     */
    isLandscape() {
        return this.orientation === 'landscape';
    }

    /**
     * 获取性能设置
     * @param {string} breakpoint 断点（可选）
     * @returns {Object} 性能设置
     */
    getPerformanceSettings(breakpoint = null) {
        const bp = breakpoint || this.currentBreakpoint;
        return { ...this.performanceSettings[bp] };
    }

    /**
     * 设置性能设置
     * @param {string} breakpoint 断点
     * @param {Object} settings 设置
     */
    setPerformanceSettings(breakpoint, settings) {
        this.performanceSettings[breakpoint] = {
            ...this.performanceSettings[breakpoint],
            ...settings
        };

        if (breakpoint === this.currentBreakpoint) {
            this.applyResponsiveSettings();
        }
    }

    /**
     * 获取视口信息
     * @returns {Object} 视口信息
     */
    getViewportInfo() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            pixelRatio: this.pixelRatio,
            breakpoint: this.currentBreakpoint,
            orientation: this.orientation
        };
    }

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 优化移动端性能
     */
    optimizeForMobile() {
        if (this.isMobile()) {
            // 禁用某些视觉效果
            document.body.classList.add('mobile-optimized');

            // 减少动画
            const style = document.createElement('style');
            style.textContent = `
                .mobile-optimized * {
                    animation-duration: 0.1s !important;
                    transition-duration: 0.1s !important;
                }
            `;
            document.head.appendChild(style);

            // 禁用滚动弹性（iOS）
            document.addEventListener('touchmove', (e) => {
                e.preventDefault();
            }, { passive: false });

            console.log('Mobile optimizations applied');
        }
    }

    /**
     * 销毁管理器
     */
    dispose() {
        // 移除所有事件监听器
        this.eventListeners.forEach(({ element, event, handler }) => {
            if (element && element.removeEventListener) {
                element.removeEventListener(event, handler);
            } else if (element && element.removeListener) {
                element.removeListener(handler);
            }
        });

        this.eventListeners = [];
        this.callbacks = {};

        console.log('Responsive manager disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ResponsiveManager;
} else {
    window.ResponsiveManager = ResponsiveManager;
}
