/**
 * HuntGame - 游戏HUD界面管理器
 * 管理游戏中的用户界面元素
 */

class HUDManager {
    constructor() {
        // DOM元素
        this.elements = {
            currentPlayer: document.getElementById('current-player'),
            gameStatus: document.getElementById('game-status'),
            btnHint: document.getElementById('btn-hint'),
            btnUndo: document.getElementById('btn-undo'),
            btnMenu: document.getElementById('btn-menu'),
            sidePanel: document.getElementById('side-panel'),
            btnNewGame: document.getElementById('btn-new-game'),
            btnClosePanel: document.getElementById('btn-close-panel'),
            aiDifficulty: document.getElementById('ai-difficulty'),
            enableHints: document.getElementById('enable-hints'),
            enableSound: document.getElementById('enable-sound')
        };
        
        // 状态
        this.isPanelOpen = false;
        this.gameState = null;
        
        // 事件回调
        this.callbacks = {
            onHint: null,
            onUndo: null,
            onNewGame: null,
            onSettingsChange: null
        };
        
        this.init();
    }

    /**
     * 初始化HUD管理器
     */
    init() {
        this.bindEvents();
        this.updateDisplay();
        
        console.log('HUD manager initialized');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 提示按钮
        if (this.elements.btnHint) {
            this.elements.btnHint.addEventListener('click', () => {
                this.handleHint();
            });
        }
        
        // 悔棋按钮
        if (this.elements.btnUndo) {
            this.elements.btnUndo.addEventListener('click', () => {
                this.handleUndo();
            });
        }
        
        // 菜单按钮
        if (this.elements.btnMenu) {
            this.elements.btnMenu.addEventListener('click', () => {
                this.togglePanel();
            });
        }
        
        // 新游戏按钮
        if (this.elements.btnNewGame) {
            this.elements.btnNewGame.addEventListener('click', () => {
                this.handleNewGame();
            });
        }
        
        // 关闭面板按钮
        if (this.elements.btnClosePanel) {
            this.elements.btnClosePanel.addEventListener('click', () => {
                this.closePanel();
            });
        }
        
        // AI难度选择
        if (this.elements.aiDifficulty) {
            this.elements.aiDifficulty.addEventListener('change', () => {
                this.handleSettingsChange();
            });
        }
        
        // 提示开关
        if (this.elements.enableHints) {
            this.elements.enableHints.addEventListener('change', () => {
                this.handleSettingsChange();
            });
        }
        
        // 音效开关
        if (this.elements.enableSound) {
            this.elements.enableSound.addEventListener('change', () => {
                this.handleSettingsChange();
            });
        }
        
        // 点击面板外部关闭
        document.addEventListener('click', (e) => {
            if (this.isPanelOpen && this.elements.sidePanel && 
                !this.elements.sidePanel.contains(e.target) && 
                !this.elements.btnMenu.contains(e.target)) {
                this.closePanel();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }

    /**
     * 设置事件回调
     * @param {Object} callbacks 回调函数对象
     */
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * 更新游戏状态显示
     * @param {Object} gameState 游戏状态对象
     */
    updateGameState(gameState) {
        this.gameState = gameState;
        this.updateDisplay();
    }

    /**
     * 更新显示内容
     */
    updateDisplay() {
        if (!this.gameState) return;
        
        // 更新当前玩家
        if (this.elements.currentPlayer) {
            const playerText = this.gameState.currentPlayer === 'red' ? '红方回合' : '黑方回合';
            this.elements.currentPlayer.textContent = playerText;
            
            // 更新样式
            this.elements.currentPlayer.className = 'current-player';
            if (this.gameState.currentPlayer === 'red') {
                this.elements.currentPlayer.classList.add('red-turn');
            } else {
                this.elements.currentPlayer.classList.add('black-turn');
            }
        }
        
        // 更新游戏状态
        if (this.elements.gameStatus) {
            let statusText = '';
            let statusClass = '';
            
            switch (this.gameState.gameStatus) {
                case 'playing':
                    statusText = '游戏进行中';
                    statusClass = '';
                    break;
                case 'check':
                    statusText = '将军！';
                    statusClass = 'animate-blink';
                    break;
                case 'checkmate':
                    const winner = this.gameState.winner === 'red' ? '红方' : '黑方';
                    statusText = `${winner}获胜！`;
                    statusClass = 'success-bounce';
                    break;
                case 'stalemate':
                    statusText = '平局';
                    statusClass = '';
                    break;
                case 'draw':
                    statusText = '和棋';
                    statusClass = '';
                    break;
                default:
                    statusText = '游戏进行中';
            }
            
            this.elements.gameStatus.textContent = statusText;
            this.elements.gameStatus.className = 'game-status ' + statusClass;
        }
        
        // 更新按钮状态
        this.updateButtonStates();
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        if (!this.gameState) return;
        
        const isGameActive = this.gameState.gameStatus === 'playing' || this.gameState.gameStatus === 'check';
        
        // 提示按钮
        if (this.elements.btnHint) {
            this.elements.btnHint.disabled = !isGameActive || !this.getSettings().enableHints;
        }
        
        // 悔棋按钮
        if (this.elements.btnUndo) {
            this.elements.btnUndo.disabled = !isGameActive || this.gameState.moveCount === 0;
        }
    }

    /**
     * 处理提示按钮点击
     */
    handleHint() {
        if (this.callbacks.onHint) {
            this.callbacks.onHint();
        }
        
        // 添加按钮动画
        if (this.elements.btnHint) {
            this.elements.btnHint.classList.add('animate-pulse');
            setTimeout(() => {
                this.elements.btnHint.classList.remove('animate-pulse');
            }, 600);
        }
    }

    /**
     * 处理悔棋按钮点击
     */
    handleUndo() {
        if (this.callbacks.onUndo) {
            this.callbacks.onUndo();
        }
        
        // 添加按钮动画
        if (this.elements.btnUndo) {
            this.elements.btnUndo.classList.add('animate-bounce');
            setTimeout(() => {
                this.elements.btnUndo.classList.remove('animate-bounce');
            }, 600);
        }
    }

    /**
     * 处理新游戏按钮点击
     */
    handleNewGame() {
        if (this.callbacks.onNewGame) {
            this.callbacks.onNewGame();
        }
        
        this.closePanel();
    }

    /**
     * 处理设置变化
     */
    handleSettingsChange() {
        const settings = this.getSettings();
        
        if (this.callbacks.onSettingsChange) {
            this.callbacks.onSettingsChange(settings);
        }
        
        // 保存设置到本地存储
        StorageUtils.saveSettings(settings);
        
        // 更新按钮状态
        this.updateButtonStates();
    }

    /**
     * 获取当前设置
     * @returns {Object} 设置对象
     */
    getSettings() {
        return {
            difficulty: this.elements.aiDifficulty?.value || 'medium',
            enableHints: this.elements.enableHints?.checked || false,
            enableSound: this.elements.enableSound?.checked || false
        };
    }

    /**
     * 应用设置
     * @param {Object} settings 设置对象
     */
    applySettings(settings) {
        if (this.elements.aiDifficulty) {
            this.elements.aiDifficulty.value = settings.difficulty || 'medium';
        }
        
        if (this.elements.enableHints) {
            this.elements.enableHints.checked = settings.enableHints !== false;
        }
        
        if (this.elements.enableSound) {
            this.elements.enableSound.checked = settings.enableSound !== false;
        }
        
        this.updateButtonStates();
    }

    /**
     * 切换侧边面板
     */
    togglePanel() {
        if (this.isPanelOpen) {
            this.closePanel();
        } else {
            this.openPanel();
        }
    }

    /**
     * 打开侧边面板
     */
    openPanel() {
        if (this.elements.sidePanel) {
            this.elements.sidePanel.classList.add('active');
            this.isPanelOpen = true;
        }
    }

    /**
     * 关闭侧边面板
     */
    closePanel() {
        if (this.elements.sidePanel) {
            this.elements.sidePanel.classList.remove('active');
            this.isPanelOpen = false;
        }
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} e 键盘事件
     */
    handleKeyboard(e) {
        // 如果有对话框打开，不处理快捷键
        if (document.querySelector('.dialog-overlay[style*="flex"]')) {
            return;
        }
        
        switch (e.code) {
            case 'KeyH':
                if (!e.ctrlKey && !e.altKey) {
                    e.preventDefault();
                    this.handleHint();
                }
                break;
            case 'KeyZ':
                if (e.ctrlKey && !e.shiftKey) {
                    e.preventDefault();
                    this.handleUndo();
                }
                break;
            case 'KeyN':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.handleNewGame();
                }
                break;
            case 'Escape':
                if (this.isPanelOpen) {
                    this.closePanel();
                } else {
                    this.openPanel();
                }
                break;
        }
    }

    /**
     * 显示提示信息
     * @param {string} message 提示消息
     * @param {number} duration 显示时长（毫秒）
     */
    showToast(message, duration = 3000) {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = 'toast-message';
        toast.textContent = message;
        
        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '80px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(47, 27, 20, 0.95)',
            color: '#E8D5B7',
            padding: '12px 24px',
            borderRadius: '6px',
            border: '1px solid rgba(212, 175, 55, 0.3)',
            fontSize: '14px',
            zIndex: '1001',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });
        
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);
        
        // 隐藏动画
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    /**
     * 销毁HUD管理器
     */
    dispose() {
        // 移除事件监听器
        Object.values(this.elements).forEach(element => {
            if (element && element.removeEventListener) {
                // 这里应该移除具体的事件监听器，但为了简化，我们只是清空引用
                element.onclick = null;
                element.onchange = null;
            }
        });
        
        this.closePanel();
        
        console.log('HUD manager disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HUDManager;
} else {
    window.HUDManager = HUDManager;
}
