/**
 * HuntGame - 对话框组件
 * 提供各种类型的对话框功能
 */

class DialogManager {
    constructor() {
        this.currentDialog = null;
        this.dialogQueue = [];
        
        // 获取DOM元素
        this.overlay = document.getElementById('game-dialog');
        this.content = this.overlay?.querySelector('.dialog-content');
        this.title = document.getElementById('dialog-title');
        this.message = document.getElementById('dialog-message');
        this.confirmBtn = document.getElementById('dialog-confirm');
        this.cancelBtn = document.getElementById('dialog-cancel');
        
        this.init();
    }

    /**
     * 初始化对话框管理器
     */
    init() {
        if (!this.overlay) {
            console.warn('Dialog overlay not found');
            return;
        }
        
        // 绑定事件监听器
        this.bindEvents();
        
        console.log('Dialog manager initialized');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 确认按钮
        if (this.confirmBtn) {
            this.confirmBtn.addEventListener('click', () => {
                this.handleConfirm();
            });
        }
        
        // 取消按钮
        if (this.cancelBtn) {
            this.cancelBtn.addEventListener('click', () => {
                this.handleCancel();
            });
        }
        
        // 点击遮罩层关闭（可选）
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.handleCancel();
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible()) {
                this.handleCancel();
            }
        });
    }

    /**
     * 显示确认对话框
     * @param {string} title 标题
     * @param {string} message 消息内容
     * @param {Object} options 选项
     * @returns {Promise} Promise对象
     */
    confirm(title, message, options = {}) {
        return new Promise((resolve) => {
            const dialog = {
                type: 'confirm',
                title: title,
                message: message,
                options: {
                    confirmText: options.confirmText || '确定',
                    cancelText: options.cancelText || '取消',
                    showCancel: options.showCancel !== false,
                    ...options
                },
                resolve: resolve
            };
            
            this.showDialog(dialog);
        });
    }

    /**
     * 显示警告对话框
     * @param {string} title 标题
     * @param {string} message 消息内容
     * @param {Object} options 选项
     * @returns {Promise} Promise对象
     */
    alert(title, message, options = {}) {
        return new Promise((resolve) => {
            const dialog = {
                type: 'alert',
                title: title,
                message: message,
                options: {
                    confirmText: options.confirmText || '确定',
                    showCancel: false,
                    ...options
                },
                resolve: resolve
            };
            
            this.showDialog(dialog);
        });
    }

    /**
     * 显示信息对话框
     * @param {string} title 标题
     * @param {string} message 消息内容
     * @param {Object} options 选项
     * @returns {Promise} Promise对象
     */
    info(title, message, options = {}) {
        return this.alert(title, message, {
            confirmText: '知道了',
            ...options
        });
    }

    /**
     * 显示错误对话框
     * @param {string} title 标题
     * @param {string} message 消息内容
     * @param {Object} options 选项
     * @returns {Promise} Promise对象
     */
    error(title, message, options = {}) {
        return this.alert(title || '错误', message, {
            confirmText: '确定',
            ...options
        });
    }

    /**
     * 显示成功对话框
     * @param {string} title 标题
     * @param {string} message 消息内容
     * @param {Object} options 选项
     * @returns {Promise} Promise对象
     */
    success(title, message, options = {}) {
        return this.alert(title || '成功', message, {
            confirmText: '好的',
            ...options
        });
    }

    /**
     * 显示对话框
     * @param {Object} dialog 对话框配置
     */
    showDialog(dialog) {
        // 如果有正在显示的对话框，加入队列
        if (this.currentDialog) {
            this.dialogQueue.push(dialog);
            return;
        }
        
        this.currentDialog = dialog;
        
        // 设置内容
        if (this.title) {
            this.title.textContent = dialog.title;
        }
        
        if (this.message) {
            this.message.textContent = dialog.message;
        }
        
        // 设置按钮
        if (this.confirmBtn) {
            this.confirmBtn.textContent = dialog.options.confirmText;
            this.confirmBtn.style.display = 'inline-flex';
        }
        
        if (this.cancelBtn) {
            if (dialog.options.showCancel) {
                this.cancelBtn.textContent = dialog.options.cancelText || '取消';
                this.cancelBtn.style.display = 'inline-flex';
            } else {
                this.cancelBtn.style.display = 'none';
            }
        }
        
        // 显示对话框
        this.overlay.style.display = 'flex';
        
        // 添加动画类
        if (this.content) {
            this.content.classList.add('animate-scale-in');
        }
        
        // 聚焦到确认按钮
        setTimeout(() => {
            if (this.confirmBtn) {
                this.confirmBtn.focus();
            }
        }, 100);
    }

    /**
     * 隐藏对话框
     */
    hideDialog() {
        if (!this.currentDialog) return;
        
        // 添加退出动画
        if (this.overlay) {
            this.overlay.classList.add('animate-fade-out');
        }
        
        setTimeout(() => {
            this.overlay.style.display = 'none';
            this.overlay.classList.remove('animate-fade-out');
            
            if (this.content) {
                this.content.classList.remove('animate-scale-in');
            }
            
            this.currentDialog = null;
            
            // 处理队列中的下一个对话框
            this.processQueue();
        }, 300);
    }

    /**
     * 处理队列中的对话框
     */
    processQueue() {
        if (this.dialogQueue.length > 0) {
            const nextDialog = this.dialogQueue.shift();
            setTimeout(() => {
                this.showDialog(nextDialog);
            }, 100);
        }
    }

    /**
     * 处理确认按钮点击
     */
    handleConfirm() {
        if (this.currentDialog && this.currentDialog.resolve) {
            this.currentDialog.resolve(true);
        }
        this.hideDialog();
    }

    /**
     * 处理取消按钮点击
     */
    handleCancel() {
        if (this.currentDialog && this.currentDialog.resolve) {
            this.currentDialog.resolve(false);
        }
        this.hideDialog();
    }

    /**
     * 检查对话框是否可见
     * @returns {boolean} 是否可见
     */
    isVisible() {
        return this.overlay && this.overlay.style.display !== 'none';
    }

    /**
     * 关闭所有对话框
     */
    closeAll() {
        // 清空队列
        this.dialogQueue = [];
        
        // 关闭当前对话框
        if (this.currentDialog) {
            this.handleCancel();
        }
    }

    /**
     * 显示游戏结束对话框
     * @param {string} winner 获胜者
     * @param {string} reason 结束原因
     * @returns {Promise} Promise对象
     */
    showGameOver(winner, reason) {
        let title, message;
        
        switch (reason) {
            case 'checkmate':
                title = '游戏结束';
                message = `${winner === 'red' ? '红方' : '黑方'}获胜！\n${winner === 'red' ? '黑方' : '红方'}被将死。`;
                break;
            case 'stalemate':
                title = '游戏结束';
                message = '平局！无子可动。';
                break;
            case 'draw':
                title = '游戏结束';
                message = '平局！双方同意和棋。';
                break;
            default:
                title = '游戏结束';
                message = `游戏结束，${winner === 'red' ? '红方' : '黑方'}获胜！`;
        }
        
        return this.confirm(title, message, {
            confirmText: '再来一局',
            cancelText: '返回菜单',
            showCancel: true
        });
    }

    /**
     * 显示新游戏确认对话框
     * @returns {Promise} Promise对象
     */
    showNewGameConfirm() {
        return this.confirm(
            '开始新游戏',
            '确定要开始新游戏吗？当前游戏进度将会丢失。',
            {
                confirmText: '开始新游戏',
                cancelText: '继续当前游戏'
            }
        );
    }

    /**
     * 显示退出游戏确认对话框
     * @returns {Promise} Promise对象
     */
    showExitConfirm() {
        return this.confirm(
            '退出游戏',
            '确定要退出游戏吗？游戏进度将会自动保存。',
            {
                confirmText: '退出',
                cancelText: '继续游戏'
            }
        );
    }

    /**
     * 销毁对话框管理器
     */
    dispose() {
        this.closeAll();
        
        // 移除事件监听器
        if (this.confirmBtn) {
            this.confirmBtn.removeEventListener('click', this.handleConfirm);
        }
        
        if (this.cancelBtn) {
            this.cancelBtn.removeEventListener('click', this.handleCancel);
        }
        
        console.log('Dialog manager disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DialogManager;
} else {
    window.DialogManager = DialogManager;
}
