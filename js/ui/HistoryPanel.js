/**
 * HuntGame - 游戏历史面板
 * 显示游戏记录、复盘控制的UI组件
 */

class HistoryPanel {
    constructor() {
        this.isVisible = false;
        this.gameRecord = new GameRecord();
        this.gameReplay = null;
        this.currentView = 'list'; // 'list', 'replay', 'analysis'
        
        this.createPanel();
        this.bindEvents();
        this.loadGameHistory();
        
        console.log('History panel initialized');
    }

    /**
     * 创建历史面板
     */
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.className = 'history-panel';
        this.panel.innerHTML = `
            <div class="history-header">
                <h3 class="history-title">
                    <i class="icon-history"></i>
                    游戏历史
                </h3>
                <div class="history-actions">
                    <button class="history-btn" id="export-all" title="导出所有记录">
                        <i class="icon-download"></i>
                    </button>
                    <button class="history-btn" id="clear-all" title="清空历史">
                        <i class="icon-trash"></i>
                    </button>
                    <button class="history-close-btn" title="关闭">
                        <i class="icon-close"></i>
                    </button>
                </div>
            </div>
            
            <div class="history-content">
                <!-- 游戏列表视图 -->
                <div class="history-view" data-view="list">
                    <div class="history-stats">
                        <div class="stat-item">
                            <span class="stat-label">总局数：</span>
                            <span class="stat-value" id="total-games">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">胜率：</span>
                            <span class="stat-value" id="win-rate">0%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均步数：</span>
                            <span class="stat-value" id="avg-moves">0</span>
                        </div>
                    </div>
                    
                    <div class="history-filters">
                        <select class="filter-select" id="result-filter">
                            <option value="all">所有结果</option>
                            <option value="win">胜利</option>
                            <option value="loss">失败</option>
                            <option value="draw">和棋</option>
                        </select>
                        <select class="filter-select" id="difficulty-filter">
                            <option value="all">所有难度</option>
                            <option value="easy">简单</option>
                            <option value="medium">中等</option>
                            <option value="hard">困难</option>
                            <option value="expert">专家</option>
                        </select>
                    </div>
                    
                    <div class="game-list" id="game-list">
                        <!-- 游戏记录列表 -->
                    </div>
                </div>
                
                <!-- 复盘视图 -->
                <div class="history-view" data-view="replay" style="display: none;">
                    <div class="replay-header">
                        <button class="replay-btn" id="back-to-list">
                            <i class="icon-arrow-left"></i>
                            返回列表
                        </button>
                        <div class="replay-info">
                            <span class="replay-title" id="replay-title">复盘模式</span>
                        </div>
                    </div>
                    
                    <div class="replay-controls">
                        <div class="replay-progress">
                            <div class="progress-info">
                                <span id="current-move">0</span> / <span id="total-moves">0</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="replay-progress"></div>
                            </div>
                        </div>
                        
                        <div class="replay-buttons">
                            <button class="replay-btn" id="replay-start" title="开始">
                                <i class="icon-skip-back"></i>
                            </button>
                            <button class="replay-btn" id="replay-prev" title="上一步">
                                <i class="icon-chevron-left"></i>
                            </button>
                            <button class="replay-btn" id="replay-play" title="播放/暂停">
                                <i class="icon-play"></i>
                            </button>
                            <button class="replay-btn" id="replay-next" title="下一步">
                                <i class="icon-chevron-right"></i>
                            </button>
                            <button class="replay-btn" id="replay-end" title="结束">
                                <i class="icon-skip-forward"></i>
                            </button>
                        </div>
                        
                        <div class="replay-settings">
                            <label>速度：</label>
                            <input type="range" id="replay-speed" min="100" max="3000" value="1000" step="100">
                            <span id="speed-value">1.0x</span>
                        </div>
                    </div>
                    
                    <div class="move-list">
                        <div class="move-list-header">
                            <span>步数</span>
                            <span>走法</span>
                            <span>时间</span>
                        </div>
                        <div class="move-list-content" id="move-list-content">
                            <!-- 走法列表 -->
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.panel);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭按钮
        const closeBtn = this.panel.querySelector('.history-close-btn');
        closeBtn.addEventListener('click', () => this.hide());
        
        // 导出所有记录
        const exportBtn = this.panel.querySelector('#export-all');
        exportBtn.addEventListener('click', () => this.exportAllRecords());
        
        // 清空历史
        const clearBtn = this.panel.querySelector('#clear-all');
        clearBtn.addEventListener('click', () => this.clearAllHistory());
        
        // 筛选器
        const resultFilter = this.panel.querySelector('#result-filter');
        const difficultyFilter = this.panel.querySelector('#difficulty-filter');
        resultFilter.addEventListener('change', () => this.applyFilters());
        difficultyFilter.addEventListener('change', () => this.applyFilters());
        
        // 复盘控制
        this.bindReplayControls();
        
        // 点击外部关闭
        document.addEventListener('click', (e) => {
            if (this.isVisible && !this.panel.contains(e.target)) {
                const historyButton = document.querySelector('.history-button');
                if (!historyButton || !historyButton.contains(e.target)) {
                    this.hide();
                }
            }
        });
    }

    /**
     * 绑定复盘控制事件
     */
    bindReplayControls() {
        const backBtn = this.panel.querySelector('#back-to-list');
        backBtn.addEventListener('click', () => this.showListView());
        
        const startBtn = this.panel.querySelector('#replay-start');
        startBtn.addEventListener('click', () => this.gameReplay?.goToStart());
        
        const prevBtn = this.panel.querySelector('#replay-prev');
        prevBtn.addEventListener('click', () => this.gameReplay?.previousMove());
        
        const playBtn = this.panel.querySelector('#replay-play');
        playBtn.addEventListener('click', () => this.toggleReplayPlay());
        
        const nextBtn = this.panel.querySelector('#replay-next');
        nextBtn.addEventListener('click', () => this.gameReplay?.nextMove());
        
        const endBtn = this.panel.querySelector('#replay-end');
        endBtn.addEventListener('click', () => this.gameReplay?.goToEnd());
        
        const speedSlider = this.panel.querySelector('#replay-speed');
        speedSlider.addEventListener('input', (e) => {
            const speed = parseInt(e.target.value);
            this.gameReplay?.setReplaySpeed(speed);
            this.updateSpeedDisplay(speed);
        });
    }

    /**
     * 显示面板
     */
    show() {
        this.isVisible = true;
        this.panel.classList.add('visible');
        this.loadGameHistory();
    }

    /**
     * 隐藏面板
     */
    hide() {
        this.isVisible = false;
        this.panel.classList.remove('visible');
        
        // 如果正在复盘，结束复盘
        if (this.gameReplay && this.gameReplay.isCurrentlyReplaying()) {
            this.gameReplay.endReplay();
        }
    }

    /**
     * 加载游戏历史
     */
    loadGameHistory() {
        const records = this.gameRecord.loadAllRecords();
        this.updateStats(records);
        this.displayGameList(records);
    }

    /**
     * 更新统计信息
     * @param {Array} records 游戏记录
     */
    updateStats(records) {
        const stats = this.gameRecord.getRecordStats();
        
        this.panel.querySelector('#total-games').textContent = stats.totalGames;
        
        const winRate = stats.totalGames > 0 ? 
            ((stats.wins / stats.totalGames) * 100).toFixed(1) : 0;
        this.panel.querySelector('#win-rate').textContent = winRate + '%';
        
        this.panel.querySelector('#avg-moves').textContent = Math.round(stats.averageGameLength);
    }

    /**
     * 显示游戏列表
     * @param {Array} records 游戏记录
     */
    displayGameList(records) {
        const listContainer = this.panel.querySelector('#game-list');
        listContainer.innerHTML = '';
        
        if (records.length === 0) {
            listContainer.innerHTML = `
                <div class="no-games">
                    <i class="icon-info"></i>
                    <p>还没有游戏记录</p>
                </div>
            `;
            return;
        }
        
        records.forEach(record => {
            const gameItem = this.createGameItem(record);
            listContainer.appendChild(gameItem);
        });
    }

    /**
     * 创建游戏项目元素
     * @param {Object} record 游戏记录
     * @returns {HTMLElement} 游戏项目元素
     */
    createGameItem(record) {
        const item = document.createElement('div');
        item.className = 'game-item';
        
        const date = new Date(record.startTime).toLocaleDateString();
        const time = new Date(record.startTime).toLocaleTimeString();
        const duration = this.formatDuration(record.duration);
        const result = this.getResultText(record.result, record.winner);
        const resultClass = this.getResultClass(record.result, record.winner);
        
        item.innerHTML = `
            <div class="game-info">
                <div class="game-header">
                    <span class="game-date">${date} ${time}</span>
                    <span class="game-result ${resultClass}">${result}</span>
                </div>
                <div class="game-details">
                    <span class="game-mode">${record.gameInfo.mode}</span>
                    <span class="game-difficulty">${record.gameInfo.difficulty}</span>
                    <span class="game-moves">${record.stats.totalMoves}步</span>
                    <span class="game-duration">${duration}</span>
                </div>
            </div>
            <div class="game-actions">
                <button class="game-btn replay-btn" data-action="replay" title="复盘">
                    <i class="icon-play"></i>
                </button>
                <button class="game-btn export-btn" data-action="export" title="导出">
                    <i class="icon-download"></i>
                </button>
                <button class="game-btn delete-btn" data-action="delete" title="删除">
                    <i class="icon-trash"></i>
                </button>
            </div>
        `;
        
        // 绑定按钮事件
        const replayBtn = item.querySelector('[data-action="replay"]');
        const exportBtn = item.querySelector('[data-action="export"]');
        const deleteBtn = item.querySelector('[data-action="delete"]');
        
        replayBtn.addEventListener('click', () => this.startReplay(record));
        exportBtn.addEventListener('click', () => this.exportRecord(record));
        deleteBtn.addEventListener('click', () => this.deleteRecord(record));
        
        return item;
    }

    /**
     * 开始复盘
     * @param {Object} record 游戏记录
     */
    startReplay(record) {
        if (!this.gameReplay) {
            console.warn('Game replay system not initialized');
            return;
        }
        
        // 切换到复盘视图
        this.showReplayView();
        
        // 开始复盘
        const success = this.gameReplay.startReplay(record);
        if (success) {
            this.updateReplayInfo(record);
            this.displayMoveList(record.moves);
            this.updateReplayProgress();
        }
    }

    /**
     * 显示列表视图
     */
    showListView() {
        this.currentView = 'list';
        this.panel.querySelectorAll('.history-view').forEach(view => {
            view.style.display = view.dataset.view === 'list' ? 'block' : 'none';
        });
        
        // 结束复盘
        if (this.gameReplay && this.gameReplay.isCurrentlyReplaying()) {
            this.gameReplay.endReplay();
        }
    }

    /**
     * 显示复盘视图
     */
    showReplayView() {
        this.currentView = 'replay';
        this.panel.querySelectorAll('.history-view').forEach(view => {
            view.style.display = view.dataset.view === 'replay' ? 'block' : 'none';
        });
    }

    /**
     * 更新复盘信息
     * @param {Object} record 游戏记录
     */
    updateReplayInfo(record) {
        const title = `${record.gameInfo.playerRed} vs ${record.gameInfo.playerBlack}`;
        this.panel.querySelector('#replay-title').textContent = title;
        
        const totalMoves = record.moves.length;
        this.panel.querySelector('#total-moves').textContent = totalMoves;
    }

    /**
     * 显示走法列表
     * @param {Array} moves 走法列表
     */
    displayMoveList(moves) {
        const container = this.panel.querySelector('#move-list-content');
        container.innerHTML = '';
        
        moves.forEach((move, index) => {
            const moveItem = document.createElement('div');
            moveItem.className = 'move-item';
            moveItem.innerHTML = `
                <span class="move-number">${move.moveNumber}</span>
                <span class="move-notation">${move.notation}</span>
                <span class="move-time">${(move.thinkingTime / 1000).toFixed(1)}s</span>
            `;
            
            moveItem.addEventListener('click', () => {
                this.gameReplay?.goToMove(index);
            });
            
            container.appendChild(moveItem);
        });
    }

    /**
     * 更新复盘进度
     */
    updateReplayProgress() {
        if (!this.gameReplay) return;
        
        const progress = this.gameReplay.getReplayProgress();
        
        this.panel.querySelector('#current-move').textContent = progress.current;
        this.panel.querySelector('#replay-progress').style.width = progress.percentage + '%';
        
        // 高亮当前走法
        const moveItems = this.panel.querySelectorAll('.move-item');
        moveItems.forEach((item, index) => {
            item.classList.toggle('active', index === progress.current - 1);
        });
    }

    /**
     * 切换复盘播放
     */
    toggleReplayPlay() {
        if (!this.gameReplay) return;
        
        const playBtn = this.panel.querySelector('#replay-play');
        const icon = playBtn.querySelector('i');
        
        if (this.gameReplay.isAutoPlaying()) {
            this.gameReplay.stopAutoPlay();
            icon.className = 'icon-play';
        } else {
            this.gameReplay.startAutoPlay();
            icon.className = 'icon-pause';
        }
    }

    /**
     * 更新速度显示
     * @param {number} speed 速度值
     */
    updateSpeedDisplay(speed) {
        const multiplier = (2000 - speed) / 1000;
        this.panel.querySelector('#speed-value').textContent = multiplier.toFixed(1) + 'x';
    }

    /**
     * 应用筛选器
     */
    applyFilters() {
        const resultFilter = this.panel.querySelector('#result-filter').value;
        const difficultyFilter = this.panel.querySelector('#difficulty-filter').value;
        
        let records = this.gameRecord.loadAllRecords();
        
        if (resultFilter !== 'all') {
            records = records.filter(record => {
                if (resultFilter === 'win') return record.winner === 'red';
                if (resultFilter === 'loss') return record.winner === 'black';
                if (resultFilter === 'draw') return record.result === 'draw';
                return true;
            });
        }
        
        if (difficultyFilter !== 'all') {
            records = records.filter(record => 
                record.gameInfo.difficulty === difficultyFilter
            );
        }
        
        this.displayGameList(records);
    }

    /**
     * 导出记录
     * @param {Object} record 游戏记录
     */
    exportRecord(record) {
        const pgn = this.gameRecord.exportToPGN(record.id);
        if (pgn) {
            this.downloadText(pgn, `game_${record.id}.pgn`);
        }
    }

    /**
     * 导出所有记录
     */
    exportAllRecords() {
        const records = this.gameRecord.loadAllRecords();
        const allPgn = records.map(record => 
            this.gameRecord.exportToPGN(record.id)
        ).filter(pgn => pgn).join('\n\n');
        
        if (allPgn) {
            this.downloadText(allPgn, 'huntgame_all_games.pgn');
        }
    }

    /**
     * 删除记录
     * @param {Object} record 游戏记录
     */
    async deleteRecord(record) {
        const confirmed = confirm(`确定要删除这局游戏记录吗？\n${new Date(record.startTime).toLocaleString()}`);
        
        if (confirmed) {
            const success = this.gameRecord.deleteRecord(record.id);
            if (success) {
                this.loadGameHistory();
            }
        }
    }

    /**
     * 清空所有历史
     */
    async clearAllHistory() {
        const confirmed = confirm('确定要清空所有游戏历史吗？此操作不可恢复！');
        
        if (confirmed) {
            const success = this.gameRecord.clearAllRecords();
            if (success) {
                this.loadGameHistory();
            }
        }
    }

    /**
     * 下载文本文件
     * @param {string} text 文本内容
     * @param {string} filename 文件名
     */
    downloadText(text, filename) {
        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    }

    /**
     * 设置复盘系统
     * @param {GameReplay} gameReplay 复盘系统
     */
    setGameReplay(gameReplay) {
        this.gameReplay = gameReplay;
        
        // 设置复盘回调
        if (gameReplay) {
            gameReplay.setCallbacks({
                onMoveChanged: () => this.updateReplayProgress(),
                onPositionChanged: () => this.updateReplayProgress()
            });
        }
    }

    // 辅助方法
    formatDuration(duration) {
        if (!duration) return '未知';
        
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    getResultText(result, winner) {
        if (result === 'checkmate') {
            return winner === 'red' ? '红方胜' : '黑方胜';
        } else if (result === 'draw') {
            return '和棋';
        }
        return '未完成';
    }

    getResultClass(result, winner) {
        if (result === 'checkmate') {
            return winner === 'red' ? 'win' : 'loss';
        } else if (result === 'draw') {
            return 'draw';
        }
        return 'incomplete';
    }

    /**
     * 销毁面板
     */
    dispose() {
        if (this.panel && this.panel.parentNode) {
            this.panel.parentNode.removeChild(this.panel);
        }
        
        if (this.gameReplay && this.gameReplay.isCurrentlyReplaying()) {
            this.gameReplay.endReplay();
        }
        
        console.log('History panel disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HistoryPanel;
} else {
    window.HistoryPanel = HistoryPanel;
}
