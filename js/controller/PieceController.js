/**
 * HuntGame - 棋子控制器
 * 完整的棋子交互控制系统
 */

class PieceController {
    constructor(gameEngine, boardRenderer, piecesRenderer) {
        this.gameEngine = gameEngine;
        this.boardRenderer = boardRenderer;
        this.piecesRenderer = piecesRenderer;

        // 选择状态
        this.selectedPiece = null;
        this.validMoves = [];
        this.currentPlayer = 'red';

        // 交互状态
        this.isInteractionEnabled = true;
        this.isDragging = false;
        this.draggedPiece = null;
        this.dragOffset = new THREE.Vector3();

        // 回调函数
        this.callbacks = {
            onPieceSelected: null,
            onPieceDeselected: null,
            onPieceMoved: null,
            onInvalidMove: null,
            onGameStateChanged: null
        };

        console.log('Piece controller initialized');
    }

    /**
     * 设置回调函数
     * @param {Object} callbacks 回调函数对象
     */
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * 处理棋盘点击
     * @param {number} row 行位置
     * @param {number} col 列位置
     * @param {Object} intersections 射线投射结果
     */
    handleBoardClick(row, col, intersections) {
        if (!this.isInteractionEnabled) return;

        // 检查点击位置是否有效
        if (row < 0 || row > 9 || col < 0 || col > 8) {
            return;
        }

        const clickedPiece = this.gameEngine.getPieceAt(row, col);

        if (this.selectedPiece) {
            // 已有选中棋子的情况
            if (this.selectedPiece.row === row && this.selectedPiece.col === col) {
                // 点击同一个棋子，取消选择
                this.deselectPiece();
            } else if (clickedPiece && clickedPiece.color === this.currentPlayer) {
                // 点击己方其他棋子，切换选择
                this.selectPiece(row, col);
            } else {
                // 尝试移动到目标位置
                this.attemptMove(row, col);
            }
        } else {
            // 没有选中棋子的情况
            if (clickedPiece && clickedPiece.color === this.currentPlayer) {
                // 选择己方棋子
                this.selectPiece(row, col);
            }
        }
    }

    /**
     * 选择棋子
     * @param {number} row 行位置
     * @param {number} col 列位置
     */
    selectPiece(row, col) {
        const piece = this.gameEngine.getPieceAt(row, col);

        if (!piece) {
            console.warn(`No piece at ${row}, ${col}`);
            return false;
        }

        if (piece.color !== this.currentPlayer) {
            console.warn(`Cannot select opponent's piece`);
            return false;
        }

        // 取消之前的选择
        this.deselectPiece();

        // 选择新棋子
        this.selectedPiece = { row, col, piece };

        // 获取可移动位置
        this.validMoves = this.gameEngine.getValidMoves(row, col);

        // 更新渲染
        this.piecesRenderer.selectPiece(row, col);
        this.boardRenderer.highlightPosition(row, col, true);
        this.boardRenderer.showValidMoves(this.validMoves);

        // 触发回调
        if (this.callbacks.onPieceSelected) {
            this.callbacks.onPieceSelected(this.selectedPiece, this.validMoves);
        }

        console.log(`Selected ${piece.color} ${piece.type} at ${row}, ${col}`);
        return true;
    }

    /**
     * 取消选择棋子
     */
    deselectPiece() {
        if (this.selectedPiece) {
            const { row, col } = this.selectedPiece;

            // 更新渲染
            this.piecesRenderer.deselectPiece();
            this.boardRenderer.clearHighlights();

            // 触发回调
            if (this.callbacks.onPieceDeselected) {
                this.callbacks.onPieceDeselected(this.selectedPiece);
            }

            console.log(`Deselected piece at ${row}, ${col}`);
        }

        this.selectedPiece = null;
        this.validMoves = [];
    }

    /**
     * 尝试移动棋子
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     */
    async attemptMove(toRow, toCol) {
        if (!this.selectedPiece) {
            console.warn('No piece selected');
            return false;
        }

        const { row: fromRow, col: fromCol } = this.selectedPiece;

        // 检查是否为有效移动
        const isValidMove = this.validMoves.some(move =>
            move.row === toRow && move.col === toCol
        );

        if (!isValidMove) {
            console.warn(`Invalid move from ${fromRow},${fromCol} to ${toRow},${toCol}`);

            // 触发无效移动回调
            if (this.callbacks.onInvalidMove) {
                this.callbacks.onInvalidMove(fromRow, fromCol, toRow, toCol);
            }

            // 可以添加视觉反馈，比如摇晃动画
            this.showInvalidMoveEffect();
            return false;
        }

        // 禁用交互
        this.setInteractionEnabled(false);

        try {
            // 执行移动
            const moveResult = this.gameEngine.movePiece(fromRow, fromCol, toRow, toCol);

            if (moveResult.success) {
                // 更新渲染
                await this.piecesRenderer.movePiece(fromRow, fromCol, toRow, toCol, true);

                // 清除选择状态
                this.deselectPiece();

                // 切换玩家
                this.switchPlayer();

                // 触发回调
                if (this.callbacks.onPieceMoved) {
                    this.callbacks.onPieceMoved(moveResult);
                }

                if (this.callbacks.onGameStateChanged) {
                    this.callbacks.onGameStateChanged(this.gameEngine.getGameInfo());
                }

                console.log(`Moved piece from ${fromRow},${fromCol} to ${toRow},${toCol}`);
                return true;
            } else {
                console.warn(`Move failed: ${moveResult.message}`);

                if (this.callbacks.onInvalidMove) {
                    this.callbacks.onInvalidMove(fromRow, fromCol, toRow, toCol, moveResult.message);
                }

                return false;
            }
        } catch (error) {
            console.error('Error during move:', error);
            return false;
        } finally {
            // 重新启用交互
            this.setInteractionEnabled(true);
        }
    }

    /**
     * 开始拖拽棋子
     * @param {number} row 行位置
     * @param {number} col 列位置
     * @param {THREE.Vector3} worldPos 世界坐标
     */
    startDrag(row, col, worldPos) {
        if (!this.isInteractionEnabled) return false;

        const piece = this.gameEngine.getPieceAt(row, col);
        if (!piece || piece.color !== this.currentPlayer) {
            return false;
        }

        // 选择棋子（如果还没选择）
        if (!this.selectedPiece || this.selectedPiece.row !== row || this.selectedPiece.col !== col) {
            this.selectPiece(row, col);
        }

        // 开始拖拽
        this.isDragging = true;
        this.draggedPiece = this.piecesRenderer.getPieceAt(row, col);

        if (this.draggedPiece) {
            // 计算拖拽偏移
            this.dragOffset.copy(this.draggedPiece.position).sub(worldPos);

            // 提升棋子高度
            this.draggedPiece.position.y += 0.3;

            console.log(`Started dragging piece at ${row}, ${col}`);
            return true;
        }

        return false;
    }

    /**
     * 更新拖拽
     * @param {THREE.Vector3} worldPos 世界坐标
     */
    updateDrag(worldPos) {
        if (this.isDragging && this.draggedPiece) {
            // 更新棋子位置
            const newPos = worldPos.clone().add(this.dragOffset);
            newPos.y = this.draggedPiece.position.y; // 保持高度

            this.draggedPiece.position.copy(newPos);
        }
    }

    /**
     * 结束拖拽
     * @param {number} row 目标行
     * @param {number} col 目标列
     */
    async endDrag(row, col) {
        if (!this.isDragging || !this.draggedPiece) {
            return false;
        }

        this.isDragging = false;

        // 尝试移动到目标位置
        const moveSuccess = await this.attemptMove(row, col);

        if (!moveSuccess) {
            // 移动失败，返回原位置
            const originalPos = this.boardRenderer.getBoardPosition(
                this.selectedPiece.row,
                this.selectedPiece.col
            );
            originalPos.y = GameConfig.pieces.size.height / 2;

            // 动画返回原位置
            await this.animatePieceReturn(this.draggedPiece, originalPos);
        }

        this.draggedPiece = null;
        return moveSuccess;
    }

    /**
     * 动画返回原位置
     * @param {THREE.Group} pieceGroup 棋子组
     * @param {THREE.Vector3} targetPos 目标位置
     */
    animatePieceReturn(pieceGroup, targetPos) {
        return new Promise((resolve) => {
            const startPos = pieceGroup.position.clone();
            const duration = 300;
            const startTime = Date.now();

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const easeProgress = MathUtils.easeOutCubic(progress);

                pieceGroup.position.lerpVectors(startPos, targetPos, easeProgress);

                if (progress >= 1) {
                    pieceGroup.position.copy(targetPos);
                    resolve();
                } else {
                    requestAnimationFrame(animate);
                }
            };

            animate();
        });
    }

    /**
     * 显示无效移动效果
     */
    showInvalidMoveEffect() {
        if (this.selectedPiece) {
            const pieceGroup = this.piecesRenderer.getPieceAt(
                this.selectedPiece.row,
                this.selectedPiece.col
            );

            if (pieceGroup) {
                // 摇晃动画
                this.animateShake(pieceGroup);
            }
        }
    }

    /**
     * 摇晃动画
     * @param {THREE.Group} pieceGroup 棋子组
     */
    animateShake(pieceGroup) {
        const originalPos = pieceGroup.position.clone();
        const shakeAmount = 0.1;
        const duration = 400;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress < 1) {
                // 摇晃效果
                const shake = Math.sin(progress * Math.PI * 8) * shakeAmount * (1 - progress);
                pieceGroup.position.x = originalPos.x + shake;

                requestAnimationFrame(animate);
            } else {
                // 恢复原位置
                pieceGroup.position.copy(originalPos);
            }
        };

        animate();
    }

    /**
     * 切换当前玩家
     */
    switchPlayer() {
        this.currentPlayer = this.currentPlayer === 'red' ? 'black' : 'red';
        console.log(`Switched to ${this.currentPlayer} player`);
    }

    /**
     * 设置当前玩家
     * @param {string} player 玩家颜色
     */
    setCurrentPlayer(player) {
        if (player === 'red' || player === 'black') {
            this.currentPlayer = player;
            console.log(`Current player set to ${player}`);
        }
    }

    /**
     * 获取当前玩家
     * @returns {string} 当前玩家颜色
     */
    getCurrentPlayer() {
        return this.currentPlayer;
    }

    /**
     * 获取选中的棋子
     * @returns {Object|null} 选中的棋子信息
     */
    getSelectedPiece() {
        return this.selectedPiece;
    }

    /**
     * 获取有效移动位置
     * @returns {Array} 有效移动位置数组
     */
    getValidMoves() {
        return [...this.validMoves];
    }

    /**
     * 检查是否有棋子被选中
     * @returns {boolean} 是否有选中的棋子
     */
    hasSelectedPiece() {
        return this.selectedPiece !== null;
    }

    /**
     * 检查是否正在拖拽
     * @returns {boolean} 是否正在拖拽
     */
    isDraggingPiece() {
        return this.isDragging;
    }

    /**
     * 启用/禁用交互
     * @param {boolean} enabled 是否启用
     */
    setInteractionEnabled(enabled) {
        this.isInteractionEnabled = enabled;

        if (!enabled) {
            // 如果禁用交互，取消当前选择和拖拽
            this.deselectPiece();

            if (this.isDragging) {
                this.isDragging = false;
                this.draggedPiece = null;
            }
        }

        console.log(`Piece interaction ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * 检查交互是否启用
     * @returns {boolean} 交互是否启用
     */
    isInteractionEnabledState() {
        return this.isInteractionEnabled;
    }

    /**
     * 处理游戏状态变化
     * @param {Object} gameState 游戏状态
     */
    handleGameStateChange(gameState) {
        // 根据游戏状态调整交互
        switch (gameState.gameStatus) {
            case 'checkmate':
            case 'stalemate':
            case 'draw':
                this.setInteractionEnabled(false);
                break;
            case 'playing':
            case 'check':
                this.setInteractionEnabled(true);
                break;
        }

        // 更新当前玩家
        if (gameState.currentPlayer !== this.currentPlayer) {
            this.setCurrentPlayer(gameState.currentPlayer);
            this.deselectPiece(); // 切换玩家时取消选择
        }
    }

    /**
     * 重置控制器状态
     */
    reset() {
        this.deselectPiece();
        this.currentPlayer = 'red';
        this.isDragging = false;
        this.draggedPiece = null;
        this.setInteractionEnabled(true);

        console.log('Piece controller reset');
    }

    /**
     * 获取控制器状态
     * @returns {Object} 控制器状态
     */
    getState() {
        return {
            selectedPiece: this.selectedPiece,
            validMoves: [...this.validMoves],
            currentPlayer: this.currentPlayer,
            isInteractionEnabled: this.isInteractionEnabled,
            isDragging: this.isDragging
        };
    }

    /**
     * 处理AI移动
     * @param {Object} aiMove AI移动结果
     */
    async handleAIMove(aiMove) {
        if (!aiMove || !aiMove.move) {
            console.warn('Invalid AI move');
            return false;
        }

        const { from, to } = aiMove.move;

        // 禁用交互
        this.setInteractionEnabled(false);

        try {
            // 执行AI移动
            const moveResult = this.gameEngine.movePiece(from.row, from.col, to.row, to.col);

            if (moveResult.success) {
                // 更新渲染
                await this.piecesRenderer.movePiece(from.row, from.col, to.row, to.col, true);

                // 切换玩家
                this.switchPlayer();

                // 触发回调
                if (this.callbacks.onPieceMoved) {
                    this.callbacks.onPieceMoved(moveResult);
                }

                if (this.callbacks.onGameStateChanged) {
                    this.callbacks.onGameStateChanged(this.gameEngine.getGameInfo());
                }

                console.log(`AI moved from ${from.row},${from.col} to ${to.row},${to.col}`);
                return true;
            } else {
                console.error(`AI move failed: ${moveResult.message}`);
                return false;
            }
        } catch (error) {
            console.error('Error during AI move:', error);
            return false;
        } finally {
            // 重新启用交互
            this.setInteractionEnabled(true);
        }
    }

    /**
     * 悔棋
     */
    async undoMove() {
        if (!this.isInteractionEnabled) {
            console.warn('Cannot undo: interaction disabled');
            return false;
        }

        // 取消当前选择
        this.deselectPiece();

        // 执行悔棋
        const undoResult = this.gameEngine.undo();

        if (undoResult.success) {
            // 重新创建棋子渲染
            const currentBoard = this.gameEngine.getBoard();
            this.piecesRenderer.createPiecesFromBoard(currentBoard);

            // 切换玩家
            this.switchPlayer();

            // 触发回调
            if (this.callbacks.onGameStateChanged) {
                this.callbacks.onGameStateChanged(this.gameEngine.getGameInfo());
            }

            console.log('Move undone successfully');
            return true;
        } else {
            console.warn(`Cannot undo: ${undoResult.message}`);
            return false;
        }
    }

    /**
     * 销毁控制器
     */
    dispose() {
        this.deselectPiece();
        this.callbacks = {};
        this.isDragging = false;
        this.draggedPiece = null;

        console.log('Piece controller disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PieceController;
} else {
    window.PieceController = PieceController;
}
