/**
 * HuntGame - UI控制器
 * UI控制器占位符
 */

class UIController {
    constructor() {
        this.isInitialized = true;
    }

    /**
     * 显示对话框
     * @param {string} title 标题
     * @param {string} message 消息
     */
    showDialog(title, message) {
        console.log(`Dialog: ${title} - ${message}`);
    }

    /**
     * 更新UI
     * @param {Object} gameState 游戏状态
     */
    updateUI(gameState) {
        console.log('UI updated');
    }

    /**
     * 销毁控制器
     */
    dispose() {
        console.log('UI controller disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIController;
} else {
    window.UIController = UIController;
}
