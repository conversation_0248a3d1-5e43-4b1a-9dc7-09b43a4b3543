/**
 * HuntGame - 输入管理器
 * 完整的输入管理系统，支持鼠标和触摸操作
 */

class InputManager {
    constructor(canvas, camera) {
        this.canvas = canvas;
        this.camera = camera;
        this.isEnabled = true;
        this.callbacks = {};

        // 输入状态
        this.mouse = {
            x: 0,
            y: 0,
            isDown: false,
            button: -1,
            lastClickTime: 0,
            dragStart: null,
            isDragging: false
        };

        this.touch = {
            isActive: false,
            startPos: null,
            currentPos: null,
            startTime: 0,
            isLongPress: false
        };

        // 射线投射器
        this.raycaster = new THREE.Raycaster();
        this.pointer = new THREE.Vector2();

        // 配置
        this.config = GameConfig.input;

        // 事件监听器引用（用于清理）
        this.eventListeners = [];

        this.init();
    }

    /**
     * 初始化输入管理器
     */
    init() {
        this.bindEvents();
        console.log('Input manager initialized');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 鼠标事件
        this.addEventListeners([
            { element: this.canvas, event: 'mousedown', handler: this.onMouseDown.bind(this) },
            { element: this.canvas, event: 'mousemove', handler: this.onMouseMove.bind(this) },
            { element: this.canvas, event: 'mouseup', handler: this.onMouseUp.bind(this) },
            { element: this.canvas, event: 'click', handler: this.onClick.bind(this) },
            { element: this.canvas, event: 'dblclick', handler: this.onDoubleClick.bind(this) },
            { element: this.canvas, event: 'wheel', handler: this.onWheel.bind(this) },
            { element: this.canvas, event: 'contextmenu', handler: this.onContextMenu.bind(this) },

            // 触摸事件
            { element: this.canvas, event: 'touchstart', handler: this.onTouchStart.bind(this) },
            { element: this.canvas, event: 'touchmove', handler: this.onTouchMove.bind(this) },
            { element: this.canvas, event: 'touchend', handler: this.onTouchEnd.bind(this) },
            { element: this.canvas, event: 'touchcancel', handler: this.onTouchCancel.bind(this) },

            // 键盘事件
            { element: document, event: 'keydown', handler: this.onKeyDown.bind(this) },
            { element: document, event: 'keyup', handler: this.onKeyUp.bind(this) },

            // 窗口事件
            { element: window, event: 'resize', handler: this.onResize.bind(this) },
            { element: window, event: 'blur', handler: this.onWindowBlur.bind(this) }
        ]);
    }

    /**
     * 添加事件监听器
     * @param {Array} listeners 监听器配置数组
     */
    addEventListeners(listeners) {
        listeners.forEach(({ element, event, handler }) => {
            element.addEventListener(event, handler, { passive: false });
            this.eventListeners.push({ element, event, handler });
        });
    }

    /**
     * 设置回调函数
     * @param {Object} callbacks 回调函数对象
     */
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * 鼠标按下事件
     * @param {MouseEvent} event 鼠标事件
     */
    onMouseDown(event) {
        if (!this.isEnabled) return;

        event.preventDefault();

        this.updateMousePosition(event);
        this.mouse.isDown = true;
        this.mouse.button = event.button;
        this.mouse.dragStart = { x: this.mouse.x, y: this.mouse.y };
        this.mouse.isDragging = false;

        if (this.callbacks.onMouseDown) {
            this.callbacks.onMouseDown(event, this.mouse);
        }
    }

    /**
     * 鼠标移动事件
     * @param {MouseEvent} event 鼠标事件
     */
    onMouseMove(event) {
        if (!this.isEnabled) return;

        this.updateMousePosition(event);

        // 检查是否开始拖拽
        if (this.mouse.isDown && !this.mouse.isDragging && this.mouse.dragStart) {
            const dx = this.mouse.x - this.mouse.dragStart.x;
            const dy = this.mouse.y - this.mouse.dragStart.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 5) { // 拖拽阈值
                this.mouse.isDragging = true;

                if (this.callbacks.onDragStart) {
                    this.callbacks.onDragStart(event, this.mouse);
                }
            }
        }

        if (this.mouse.isDragging) {
            if (this.callbacks.onDrag) {
                this.callbacks.onDrag(event, this.mouse);
            }
        } else {
            if (this.callbacks.onMouseMove) {
                this.callbacks.onMouseMove(event, this.mouse);
            }
        }
    }

    /**
     * 鼠标抬起事件
     * @param {MouseEvent} event 鼠标事件
     */
    onMouseUp(event) {
        if (!this.isEnabled) return;

        event.preventDefault();

        if (this.mouse.isDragging) {
            if (this.callbacks.onDragEnd) {
                this.callbacks.onDragEnd(event, this.mouse);
            }
        }

        this.mouse.isDown = false;
        this.mouse.isDragging = false;
        this.mouse.dragStart = null;

        if (this.callbacks.onMouseUp) {
            this.callbacks.onMouseUp(event, this.mouse);
        }
    }

    /**
     * 鼠标点击事件
     * @param {MouseEvent} event 鼠标事件
     */
    onClick(event) {
        if (!this.isEnabled) return;

        event.preventDefault();

        this.updateMousePosition(event);

        // 检查双击
        const currentTime = Date.now();
        const isDoubleClick = currentTime - this.mouse.lastClickTime < this.config.mouse.doubleClickTime;
        this.mouse.lastClickTime = currentTime;

        // 执行射线投射
        const intersections = this.performRaycast();

        if (this.callbacks.onClick) {
            this.callbacks.onClick(event, this.mouse, intersections, isDoubleClick);
        }
    }

    /**
     * 双击事件
     * @param {MouseEvent} event 鼠标事件
     */
    onDoubleClick(event) {
        if (!this.isEnabled) return;

        event.preventDefault();

        if (this.callbacks.onDoubleClick) {
            this.callbacks.onDoubleClick(event, this.mouse);
        }
    }

    /**
     * 鼠标滚轮事件
     * @param {WheelEvent} event 滚轮事件
     */
    onWheel(event) {
        if (!this.isEnabled) return;

        event.preventDefault();

        if (this.callbacks.onWheel) {
            this.callbacks.onWheel(event, {
                deltaX: event.deltaX,
                deltaY: event.deltaY,
                deltaZ: event.deltaZ
            });
        }
    }

    /**
     * 右键菜单事件
     * @param {MouseEvent} event 鼠标事件
     */
    onContextMenu(event) {
        event.preventDefault(); // 阻止默认右键菜单

        if (this.callbacks.onContextMenu) {
            this.callbacks.onContextMenu(event, this.mouse);
        }
    }

    /**
     * 触摸开始事件
     * @param {TouchEvent} event 触摸事件
     */
    onTouchStart(event) {
        if (!this.isEnabled) return;

        event.preventDefault();

        const touch = event.touches[0];
        if (touch) {
            this.touch.isActive = true;
            this.touch.startPos = this.getTouchPosition(touch);
            this.touch.currentPos = { ...this.touch.startPos };
            this.touch.startTime = Date.now();
            this.touch.isLongPress = false;

            // 设置长按检测
            setTimeout(() => {
                if (this.touch.isActive && !this.touch.isLongPress) {
                    const distance = this.getTouchDistance(this.touch.startPos, this.touch.currentPos);
                    if (distance < 10) { // 长按阈值
                        this.touch.isLongPress = true;

                        if (this.callbacks.onLongPress) {
                            this.callbacks.onLongPress(event, this.touch);
                        }
                    }
                }
            }, this.config.touch.longPressTime);

            if (this.callbacks.onTouchStart) {
                this.callbacks.onTouchStart(event, this.touch);
            }
        }
    }

    /**
     * 触摸移动事件
     * @param {TouchEvent} event 触摸事件
     */
    onTouchMove(event) {
        if (!this.isEnabled) return;

        event.preventDefault();

        const touch = event.touches[0];
        if (touch && this.touch.isActive) {
            this.touch.currentPos = this.getTouchPosition(touch);

            if (this.callbacks.onTouchMove) {
                this.callbacks.onTouchMove(event, this.touch);
            }
        }
    }

    /**
     * 触摸结束事件
     * @param {TouchEvent} event 触摸事件
     */
    onTouchEnd(event) {
        if (!this.isEnabled) return;

        event.preventDefault();

        if (this.touch.isActive) {
            const touchDuration = Date.now() - this.touch.startTime;
            const distance = this.getTouchDistance(this.touch.startPos, this.touch.currentPos);

            // 判断是否为点击
            if (touchDuration < this.config.touch.tapTime && distance < 10) {
                // 执行射线投射
                this.updatePointerFromTouch(this.touch.currentPos);
                const intersections = this.performRaycast();

                if (this.callbacks.onTap) {
                    this.callbacks.onTap(event, this.touch, intersections);
                }
            }

            if (this.callbacks.onTouchEnd) {
                this.callbacks.onTouchEnd(event, this.touch);
            }

            this.resetTouch();
        }
    }

    /**
     * 触摸取消事件
     * @param {TouchEvent} event 触摸事件
     */
    onTouchCancel(event) {
        if (!this.isEnabled) return;

        if (this.callbacks.onTouchCancel) {
            this.callbacks.onTouchCancel(event, this.touch);
        }

        this.resetTouch();
    }

    /**
     * 键盘按下事件
     * @param {KeyboardEvent} event 键盘事件
     */
    onKeyDown(event) {
        if (!this.isEnabled) return;

        // 检查是否有对话框打开
        if (document.querySelector('.dialog-overlay[style*="flex"]')) {
            return;
        }

        if (this.callbacks.onKeyDown) {
            this.callbacks.onKeyDown(event);
        }
    }

    /**
     * 键盘抬起事件
     * @param {KeyboardEvent} event 键盘事件
     */
    onKeyUp(event) {
        if (!this.isEnabled) return;

        if (this.callbacks.onKeyUp) {
            this.callbacks.onKeyUp(event);
        }
    }

    /**
     * 窗口大小改变事件
     * @param {Event} event 事件
     */
    onResize(event) {
        if (this.callbacks.onResize) {
            this.callbacks.onResize(event);
        }
    }

    /**
     * 窗口失焦事件
     * @param {Event} event 事件
     */
    onWindowBlur(event) {
        // 重置所有输入状态
        this.mouse.isDown = false;
        this.mouse.isDragging = false;
        this.resetTouch();

        if (this.callbacks.onWindowBlur) {
            this.callbacks.onWindowBlur(event);
        }
    }

    /**
     * 更新鼠标位置
     * @param {MouseEvent} event 鼠标事件
     */
    updateMousePosition(event) {
        const rect = this.canvas.getBoundingClientRect();
        this.mouse.x = event.clientX - rect.left;
        this.mouse.y = event.clientY - rect.top;

        // 更新射线投射指针
        this.pointer.x = (this.mouse.x / this.canvas.clientWidth) * 2 - 1;
        this.pointer.y = -(this.mouse.y / this.canvas.clientHeight) * 2 + 1;
    }

    /**
     * 获取触摸位置
     * @param {Touch} touch 触摸对象
     * @returns {Object} 位置对象
     */
    getTouchPosition(touch) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: touch.clientX - rect.left,
            y: touch.clientY - rect.top
        };
    }

    /**
     * 计算触摸距离
     * @param {Object} pos1 位置1
     * @param {Object} pos2 位置2
     * @returns {number} 距离
     */
    getTouchDistance(pos1, pos2) {
        const dx = pos2.x - pos1.x;
        const dy = pos2.y - pos1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 从触摸位置更新指针
     * @param {Object} touchPos 触摸位置
     */
    updatePointerFromTouch(touchPos) {
        this.pointer.x = (touchPos.x / this.canvas.clientWidth) * 2 - 1;
        this.pointer.y = -(touchPos.y / this.canvas.clientHeight) * 2 + 1;
    }

    /**
     * 重置触摸状态
     */
    resetTouch() {
        this.touch.isActive = false;
        this.touch.startPos = null;
        this.touch.currentPos = null;
        this.touch.startTime = 0;
        this.touch.isLongPress = false;
    }

    /**
     * 执行射线投射
     * @param {Array} objects 要检测的对象数组（可选）
     * @returns {Array} 相交对象数组
     */
    performRaycast(objects = null) {
        this.raycaster.setFromCamera(this.pointer, this.camera);

        if (objects) {
            return this.raycaster.intersectObjects(objects, true);
        } else if (this.callbacks.getRaycastObjects) {
            const raycastObjects = this.callbacks.getRaycastObjects();
            return this.raycaster.intersectObjects(raycastObjects, true);
        }

        return [];
    }

    /**
     * 获取当前鼠标的世界坐标
     * @param {number} y 世界Y坐标（默认为0）
     * @returns {THREE.Vector3} 世界坐标
     */
    getWorldPosition(y = 0) {
        this.raycaster.setFromCamera(this.pointer, this.camera);

        // 创建一个水平面来计算交点
        const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), -y);
        const intersection = new THREE.Vector3();

        if (this.raycaster.ray.intersectPlane(plane, intersection)) {
            return intersection;
        }

        return null;
    }

    /**
     * 获取棋盘位置
     * @returns {Object|null} {row, col} 或 null
     */
    getBoardPosition() {
        const worldPos = this.getWorldPosition(0);
        if (worldPos) {
            return MathUtils.worldToBoard(worldPos.x, worldPos.z);
        }
        return null;
    }

    /**
     * 启用/禁用输入
     * @param {boolean} enabled 是否启用
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;

        if (!enabled) {
            // 重置所有状态
            this.mouse.isDown = false;
            this.mouse.isDragging = false;
            this.resetTouch();
        }
    }

    /**
     * 检查是否启用
     * @returns {boolean} 是否启用
     */
    isInputEnabled() {
        return this.isEnabled;
    }

    /**
     * 获取当前输入状态
     * @returns {Object} 输入状态
     */
    getInputState() {
        return {
            mouse: { ...this.mouse },
            touch: { ...this.touch },
            isEnabled: this.isEnabled
        };
    }

    /**
     * 销毁管理器
     */
    dispose() {
        // 移除所有事件监听器
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });

        this.eventListeners = [];
        this.callbacks = {};

        // 重置状态
        this.mouse.isDown = false;
        this.mouse.isDragging = false;
        this.resetTouch();

        console.log('Input manager disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InputManager;
} else {
    window.InputManager = InputManager;
}
