/**
 * HuntGame - 3D中国象棋游戏配置文件
 * 包含游戏的所有配置参数和常量定义
 */

// 游戏配置对象
const GameConfig = {
    // 游戏基本信息
    name: 'HuntGame',
    version: '1.0.0',
    author: 'HuntGame Team',
    
    // 游戏设置
    game: {
        // 默认游戏模式
        defaultMode: 'ai', // 'ai' | 'human'
        
        // 默认AI难度
        defaultDifficulty: 'medium', // 'easy' | 'medium' | 'hard' | 'expert'
        
        // 是否启用提示
        enableHints: true,
        
        // 是否启用音效
        enableSound: true,
        
        // 是否启用动画
        enableAnimation: true,
        
        // 自动保存间隔（毫秒）
        autoSaveInterval: 30000,
        
        // 最大历史记录数
        maxHistoryCount: 100
    },
    
    // 3D渲染配置
    render: {
        // 渲染器设置
        renderer: {
            antialias: true,
            alpha: true,
            shadowMapEnabled: true,
            shadowMapType: 'PCFSoftShadowMap',
            gammaOutput: true,
            gammaFactor: 2.2
        },
        
        // 相机设置
        camera: {
            fov: 45,
            near: 0.1,
            far: 1000,
            position: {
                x: 0,
                y: 12,
                z: 8
            },
            target: {
                x: 0,
                y: 0,
                z: 0
            }
        },
        
        // 光照设置
        lighting: {
            ambient: {
                color: 0x404040,
                intensity: 0.4
            },
            directional: {
                color: 0xffffff,
                intensity: 0.8,
                position: {
                    x: 10,
                    y: 20,
                    z: 10
                },
                castShadow: true,
                shadowMapSize: 2048
            },
            point: {
                color: 0xffd700,
                intensity: 0.3,
                position: {
                    x: 0,
                    y: 10,
                    z: 0
                },
                distance: 50
            }
        },
        
        // 场景设置
        scene: {
            background: 0x2F1B14,
            fog: {
                color: 0x2F1B14,
                near: 20,
                far: 100
            }
        }
    },
    
    // 棋盘配置
    board: {
        // 棋盘尺寸
        size: {
            width: 8,
            height: 9
        },
        
        // 格子大小
        cellSize: 1,
        
        // 棋盘材质
        material: {
            color: 0x8B4513,
            roughness: 0.8,
            metalness: 0.1
        },
        
        // 线条颜色
        lineColor: 0x2F1B14,
        
        // 高亮颜色
        highlightColor: 0xFFD700,
        
        // 选中颜色
        selectedColor: 0xFF6B6B
    },
    
    // 棋子配置
    pieces: {
        // 棋子高度
        height: 0.3,
        
        // 棋子半径
        radius: 0.4,
        
        // 红方材质
        redMaterial: {
            color: 0xDC143C,
            roughness: 0.3,
            metalness: 0.7
        },
        
        // 黑方材质
        blackMaterial: {
            color: 0x2F4F4F,
            roughness: 0.3,
            metalness: 0.7
        },
        
        // 棋子文字颜色
        textColor: {
            red: 0xFFFFFF,
            black: 0xFFFFFF
        },
        
        // 动画配置
        animation: {
            moveDuration: 500,
            captureDuration: 300,
            hoverHeight: 0.2
        }
    },
    
    // AI配置
    ai: {
        // 难度设置
        difficulty: {
            easy: {
                searchDepth: 2,
                thinkTime: 500,
                randomness: 0.3
            },
            medium: {
                searchDepth: 3,
                thinkTime: 1000,
                randomness: 0.2
            },
            hard: {
                searchDepth: 4,
                thinkTime: 2000,
                randomness: 0.1
            },
            expert: {
                searchDepth: 5,
                thinkTime: 3000,
                randomness: 0.05
            }
        },
        
        // 评估权重
        evaluation: {
            material: 1.0,
            position: 0.3,
            mobility: 0.2,
            safety: 0.4,
            development: 0.1
        },
        
        // 棋子价值
        pieceValues: {
            pawn: 100,
            cannon: 450,
            chariot: 900,
            horse: 400,
            elephant: 200,
            advisor: 200,
            king: 10000
        }
    },
    
    // 音效配置
    audio: {
        // 音效文件路径
        sounds: {
            move: 'assets/sounds/move.mp3',
            capture: 'assets/sounds/capture.mp3',
            check: 'assets/sounds/check.mp3',
            gameOver: 'assets/sounds/gameover.mp3',
            button: 'assets/sounds/button.mp3'
        },
        
        // 音量设置
        volume: {
            master: 0.7,
            effects: 0.8,
            ui: 0.6
        }
    },
    
    // 输入配置
    input: {
        // 鼠标设置
        mouse: {
            sensitivity: 1.0,
            doubleClickTime: 300
        },
        
        // 触摸设置
        touch: {
            sensitivity: 1.2,
            tapTime: 200,
            longPressTime: 500
        },
        
        // 键盘快捷键
        keyboard: {
            undo: 'KeyZ',
            hint: 'KeyH',
            menu: 'Escape',
            newGame: 'KeyN'
        }
    },
    
    // 性能配置
    performance: {
        // 目标帧率
        targetFPS: 60,
        
        // 自适应质量
        adaptiveQuality: true,
        
        // 最小帧率阈值
        minFPS: 30,
        
        // 性能监控
        enableStats: false,
        
        // 内存管理
        memoryManagement: {
            texturePoolSize: 50,
            geometryPoolSize: 100,
            materialPoolSize: 20
        }
    },
    
    // 本地存储配置
    storage: {
        // 存储键名前缀
        prefix: 'huntgame_',
        
        // 存储项
        keys: {
            gameState: 'game_state',
            settings: 'settings',
            history: 'history',
            statistics: 'statistics'
        },
        
        // 数据压缩
        compression: true,
        
        // 自动清理过期数据
        autoCleanup: true,
        
        // 数据过期时间（天）
        expireDays: 30
    },
    
    // 调试配置
    debug: {
        // 是否启用调试模式
        enabled: false,
        
        // 显示FPS
        showFPS: false,
        
        // 显示坐标轴
        showAxes: false,
        
        // 显示网格
        showGrid: false,
        
        // 控制台日志级别
        logLevel: 'info', // 'debug' | 'info' | 'warn' | 'error'
        
        // AI思考过程可视化
        showAIThinking: false
    }
};

// 导出配置对象
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameConfig;
} else {
    window.GameConfig = GameConfig;
}
