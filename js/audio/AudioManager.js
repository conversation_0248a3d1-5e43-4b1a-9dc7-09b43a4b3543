/**
 * HuntGame - 音效管理系统
 * 完整的游戏音效和背景音乐管理
 */

class AudioManager {
    constructor() {
        this.isEnabled = true;
        this.masterVolume = 0.7;
        this.sfxVolume = 0.8;
        this.musicVolume = 0.5;
        
        // 音频上下文
        this.audioContext = null;
        this.audioBuffers = new Map();
        this.activeSources = new Set();
        
        // 音频文件路径
        this.audioFiles = {
            // 游戏音效
            move: 'assets/audio/sfx/move.mp3',
            capture: 'assets/audio/sfx/capture.mp3',
            check: 'assets/audio/sfx/check.mp3',
            checkmate: 'assets/audio/sfx/checkmate.mp3',
            select: 'assets/audio/sfx/select.mp3',
            invalid: 'assets/audio/sfx/invalid.mp3',
            hint: 'assets/audio/sfx/hint.mp3',
            undo: 'assets/audio/sfx/undo.mp3',
            
            // UI音效
            click: 'assets/audio/ui/click.mp3',
            hover: 'assets/audio/ui/hover.mp3',
            open: 'assets/audio/ui/open.mp3',
            close: 'assets/audio/ui/close.mp3',
            success: 'assets/audio/ui/success.mp3',
            error: 'assets/audio/ui/error.mp3',
            
            // 背景音乐
            menu: 'assets/audio/music/menu.mp3',
            game: 'assets/audio/music/game.mp3',
            victory: 'assets/audio/music/victory.mp3',
            defeat: 'assets/audio/music/defeat.mp3'
        };
        
        // 当前播放的背景音乐
        this.currentMusic = null;
        this.musicSource = null;
        this.musicGainNode = null;
        
        // 音效增益节点
        this.sfxGainNode = null;
        
        this.init();
    }

    /**
     * 初始化音频系统
     */
    async init() {
        try {
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建增益节点
            this.sfxGainNode = this.audioContext.createGain();
            this.sfxGainNode.connect(this.audioContext.destination);
            this.sfxGainNode.gain.value = this.sfxVolume * this.masterVolume;
            
            this.musicGainNode = this.audioContext.createGain();
            this.musicGainNode.connect(this.audioContext.destination);
            this.musicGainNode.gain.value = this.musicVolume * this.masterVolume;
            
            console.log('Audio manager initialized');
            
            // 预加载关键音效
            await this.preloadAudio(['move', 'capture', 'select', 'click']);
            
        } catch (error) {
            console.warn('Failed to initialize audio:', error);
            this.isEnabled = false;
        }
    }

    /**
     * 预加载音频文件
     * @param {Array} audioKeys 音频键名数组
     */
    async preloadAudio(audioKeys = []) {
        if (!this.isEnabled || !this.audioContext) return;
        
        const loadPromises = audioKeys.map(key => this.loadAudio(key));
        
        try {
            await Promise.all(loadPromises);
            console.log('Audio preloading completed');
        } catch (error) {
            console.warn('Some audio files failed to load:', error);
        }
    }

    /**
     * 加载单个音频文件
     * @param {string} key 音频键名
     * @returns {Promise<AudioBuffer>} 音频缓冲区
     */
    async loadAudio(key) {
        if (!this.isEnabled || !this.audioContext) return null;
        
        if (this.audioBuffers.has(key)) {
            return this.audioBuffers.get(key);
        }
        
        const filePath = this.audioFiles[key];
        if (!filePath) {
            console.warn(`Audio file not found for key: ${key}`);
            return null;
        }
        
        try {
            const response = await fetch(filePath);
            const arrayBuffer = await response.arrayBuffer();
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            
            this.audioBuffers.set(key, audioBuffer);
            return audioBuffer;
            
        } catch (error) {
            console.warn(`Failed to load audio: ${filePath}`, error);
            return null;
        }
    }

    /**
     * 播放音效
     * @param {string} key 音效键名
     * @param {Object} options 播放选项
     */
    async playSFX(key, options = {}) {
        if (!this.isEnabled || !this.audioContext) return;
        
        // 恢复音频上下文（用户交互后）
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
        
        let audioBuffer = this.audioBuffers.get(key);
        
        if (!audioBuffer) {
            audioBuffer = await this.loadAudio(key);
        }
        
        if (!audioBuffer) return;
        
        try {
            const source = this.audioContext.createBufferSource();
            source.buffer = audioBuffer;
            
            // 创建增益节点用于音量控制
            const gainNode = this.audioContext.createGain();
            gainNode.gain.value = (options.volume || 1.0) * this.sfxVolume * this.masterVolume;
            
            // 连接音频图
            source.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            // 设置播放参数
            if (options.loop) {
                source.loop = true;
            }
            
            if (options.playbackRate) {
                source.playbackRate.value = options.playbackRate;
            }
            
            // 播放
            source.start(0);
            
            // 跟踪活动源
            this.activeSources.add(source);
            
            // 播放结束后清理
            source.onended = () => {
                this.activeSources.delete(source);
            };
            
            return source;
            
        } catch (error) {
            console.warn(`Failed to play SFX: ${key}`, error);
        }
    }

    /**
     * 播放背景音乐
     * @param {string} key 音乐键名
     * @param {Object} options 播放选项
     */
    async playMusic(key, options = {}) {
        if (!this.isEnabled || !this.audioContext) return;
        
        // 停止当前音乐
        this.stopMusic();
        
        // 恢复音频上下文
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
        
        let audioBuffer = this.audioBuffers.get(key);
        
        if (!audioBuffer) {
            audioBuffer = await this.loadAudio(key);
        }
        
        if (!audioBuffer) return;
        
        try {
            this.musicSource = this.audioContext.createBufferSource();
            this.musicSource.buffer = audioBuffer;
            this.musicSource.loop = options.loop !== false; // 默认循环
            
            // 连接到音乐增益节点
            this.musicSource.connect(this.musicGainNode);
            
            // 淡入效果
            if (options.fadeIn) {
                this.musicGainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                this.musicGainNode.gain.linearRampToValueAtTime(
                    this.musicVolume * this.masterVolume,
                    this.audioContext.currentTime + options.fadeIn
                );
            }
            
            this.musicSource.start(0);
            this.currentMusic = key;
            
            // 播放结束处理
            this.musicSource.onended = () => {
                this.currentMusic = null;
                this.musicSource = null;
            };
            
            console.log(`Playing music: ${key}`);
            
        } catch (error) {
            console.warn(`Failed to play music: ${key}`, error);
        }
    }

    /**
     * 停止背景音乐
     * @param {number} fadeOut 淡出时间（秒）
     */
    stopMusic(fadeOut = 0) {
        if (!this.musicSource) return;
        
        if (fadeOut > 0) {
            // 淡出效果
            this.musicGainNode.gain.linearRampToValueAtTime(
                0,
                this.audioContext.currentTime + fadeOut
            );
            
            setTimeout(() => {
                if (this.musicSource) {
                    this.musicSource.stop();
                    this.musicSource = null;
                    this.currentMusic = null;
                }
            }, fadeOut * 1000);
        } else {
            // 立即停止
            this.musicSource.stop();
            this.musicSource = null;
            this.currentMusic = null;
        }
    }

    /**
     * 暂停/恢复背景音乐
     */
    toggleMusic() {
        if (!this.audioContext) return;
        
        if (this.audioContext.state === 'running') {
            this.audioContext.suspend();
        } else {
            this.audioContext.resume();
        }
    }

    /**
     * 停止所有音效
     */
    stopAllSFX() {
        this.activeSources.forEach(source => {
            try {
                source.stop();
            } catch (error) {
                // 忽略已经停止的源
            }
        });
        this.activeSources.clear();
    }

    /**
     * 设置主音量
     * @param {number} volume 音量 (0-1)
     */
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        this.updateVolumes();
    }

    /**
     * 设置音效音量
     * @param {number} volume 音量 (0-1)
     */
    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        this.updateVolumes();
    }

    /**
     * 设置音乐音量
     * @param {number} volume 音量 (0-1)
     */
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        this.updateVolumes();
    }

    /**
     * 更新所有音量
     */
    updateVolumes() {
        if (this.sfxGainNode) {
            this.sfxGainNode.gain.value = this.sfxVolume * this.masterVolume;
        }
        
        if (this.musicGainNode) {
            this.musicGainNode.gain.value = this.musicVolume * this.masterVolume;
        }
    }

    /**
     * 启用/禁用音频
     * @param {boolean} enabled 是否启用
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        
        if (!enabled) {
            this.stopAllSFX();
            this.stopMusic();
        }
    }

    /**
     * 获取音频状态
     * @returns {Object} 音频状态
     */
    getAudioState() {
        return {
            isEnabled: this.isEnabled,
            masterVolume: this.masterVolume,
            sfxVolume: this.sfxVolume,
            musicVolume: this.musicVolume,
            currentMusic: this.currentMusic,
            audioContext: this.audioContext ? this.audioContext.state : 'unavailable',
            loadedAudio: Array.from(this.audioBuffers.keys())
        };
    }

    /**
     * 创建音频可视化器（可选功能）
     * @returns {AnalyserNode|null} 分析器节点
     */
    createVisualizer() {
        if (!this.audioContext) return null;
        
        const analyser = this.audioContext.createAnalyser();
        analyser.fftSize = 256;
        
        // 连接到音频图
        this.musicGainNode.connect(analyser);
        
        return analyser;
    }

    /**
     * 销毁音频管理器
     */
    dispose() {
        this.stopAllSFX();
        this.stopMusic();
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        this.audioBuffers.clear();
        this.activeSources.clear();
        
        console.log('Audio manager disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioManager;
} else {
    window.AudioManager = AudioManager;
}
