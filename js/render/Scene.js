/**
 * HuntGame - 3D场景管理器
 * 负责创建和管理整个3D游戏场景
 */

class SceneManager {
    constructor() {
        this.scene = null;
        this.renderer = null;
        this.camera = null;
        this.cameraController = null;
        this.boardRenderer = null;
        this.piecesRenderer = null;
        
        // 光照对象
        this.lights = {
            ambient: null,
            directional: null,
            point: null
        };
        
        // 场景状态
        this.isInitialized = false;
        this.isRendering = false;
        this.animationId = null;
        
        // 性能监控
        this.stats = null;
        this.frameCount = 0;
        this.lastTime = 0;
        this.fps = 0;
        
        this.init();
    }

    /**
     * 初始化3D场景
     */
    init() {
        try {
            this.createRenderer();
            this.createScene();
            this.createLights();
            this.createCamera();
            this.setupEventListeners();
            
            // 创建子渲染器
            this.boardRenderer = new BoardRenderer(this.scene);
            this.piecesRenderer = new PiecesRenderer(this.scene);
            
            // 初始化性能监控
            if (GameConfig.debug.enabled && GameConfig.debug.showFPS) {
                this.initStats();
            }
            
            this.isInitialized = true;
            console.log('3D Scene initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize 3D scene:', error);
            throw error;
        }
    }

    /**
     * 创建WebGL渲染器
     */
    createRenderer() {
        const canvas = document.getElementById('game-canvas');
        if (!canvas) {
            throw new Error('Game canvas not found');
        }

        const config = GameConfig.render.renderer;
        
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: config.antialias,
            alpha: config.alpha
        });
        
        // 设置渲染器属性
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // 启用阴影
        if (config.shadowMapEnabled) {
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = THREE[config.shadowMapType] || THREE.PCFSoftShadowMap;
        }
        
        // 设置色彩空间
        if (config.gammaOutput) {
            this.renderer.outputEncoding = THREE.sRGBEncoding;
            this.renderer.gammaFactor = config.gammaFactor;
        }
        
        // 设置背景色
        this.renderer.setClearColor(GameConfig.render.scene.background, 1);
        
        console.log('WebGL Renderer created');
    }

    /**
     * 创建3D场景
     */
    createScene() {
        this.scene = new THREE.Scene();
        
        // 设置雾效
        const fogConfig = GameConfig.render.scene.fog;
        if (fogConfig) {
            this.scene.fog = new THREE.Fog(
                fogConfig.color,
                fogConfig.near,
                fogConfig.far
            );
        }
        
        // 添加调试辅助对象
        if (GameConfig.debug.enabled) {
            if (GameConfig.debug.showAxes) {
                const axesHelper = new THREE.AxesHelper(5);
                this.scene.add(axesHelper);
            }
            
            if (GameConfig.debug.showGrid) {
                const gridHelper = new THREE.GridHelper(20, 20);
                this.scene.add(gridHelper);
            }
        }
        
        console.log('3D Scene created');
    }

    /**
     * 创建光照系统
     */
    createLights() {
        const lightConfig = GameConfig.render.lighting;
        
        // 环境光
        this.lights.ambient = new THREE.AmbientLight(
            lightConfig.ambient.color,
            lightConfig.ambient.intensity
        );
        this.scene.add(this.lights.ambient);
        
        // 方向光（主光源）
        this.lights.directional = new THREE.DirectionalLight(
            lightConfig.directional.color,
            lightConfig.directional.intensity
        );
        
        const dirPos = lightConfig.directional.position;
        this.lights.directional.position.set(dirPos.x, dirPos.y, dirPos.z);
        
        // 配置阴影
        if (lightConfig.directional.castShadow) {
            this.lights.directional.castShadow = true;
            this.lights.directional.shadow.mapSize.width = lightConfig.directional.shadowMapSize;
            this.lights.directional.shadow.mapSize.height = lightConfig.directional.shadowMapSize;
            this.lights.directional.shadow.camera.near = 0.5;
            this.lights.directional.shadow.camera.far = 50;
            this.lights.directional.shadow.camera.left = -10;
            this.lights.directional.shadow.camera.right = 10;
            this.lights.directional.shadow.camera.top = 10;
            this.lights.directional.shadow.camera.bottom = -10;
        }
        
        this.scene.add(this.lights.directional);
        
        // 点光源（装饰光）
        this.lights.point = new THREE.PointLight(
            lightConfig.point.color,
            lightConfig.point.intensity,
            lightConfig.point.distance
        );
        
        const pointPos = lightConfig.point.position;
        this.lights.point.position.set(pointPos.x, pointPos.y, pointPos.z);
        this.scene.add(this.lights.point);
        
        console.log('Lighting system created');
    }

    /**
     * 创建相机控制器
     */
    createCamera() {
        this.cameraController = new CameraController(this.scene, this.renderer);
        this.camera = this.cameraController.camera;
        
        console.log('Camera controller created');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseRendering();
            } else {
                this.resumeRendering();
            }
        });
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        const canvas = this.renderer.domElement;
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;
        
        // 更新渲染器大小
        this.renderer.setSize(width, height);
        
        // 更新相机
        if (this.cameraController) {
            this.cameraController.resize();
        }
        
        console.log(`Scene resized to ${width}x${height}`);
    }

    /**
     * 开始渲染循环
     */
    startRendering() {
        if (this.isRendering) return;
        
        this.isRendering = true;
        this.lastTime = performance.now();
        this.renderLoop();
        
        console.log('Rendering started');
    }

    /**
     * 停止渲染循环
     */
    stopRendering() {
        if (!this.isRendering) return;
        
        this.isRendering = false;
        
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        
        console.log('Rendering stopped');
    }

    /**
     * 暂停渲染
     */
    pauseRendering() {
        if (this.isRendering) {
            this.stopRendering();
            this._wasPaused = true;
        }
    }

    /**
     * 恢复渲染
     */
    resumeRendering() {
        if (this._wasPaused) {
            this.startRendering();
            this._wasPaused = false;
        }
    }

    /**
     * 渲染循环
     */
    renderLoop() {
        if (!this.isRendering) return;
        
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // 更新FPS计算
        this.frameCount++;
        if (this.frameCount % 60 === 0) {
            this.fps = Math.round(1000 / (deltaTime || 1));
        }
        
        // 更新相机
        if (this.cameraController) {
            this.cameraController.update();
        }
        
        // 更新子渲染器
        if (this.boardRenderer) {
            this.boardRenderer.update(deltaTime);
        }
        
        if (this.piecesRenderer) {
            this.piecesRenderer.update(deltaTime);
        }
        
        // 渲染场景
        this.renderer.render(this.scene, this.camera);
        
        // 更新性能统计
        if (this.stats) {
            this.stats.update();
        }
        
        // 请求下一帧
        this.animationId = requestAnimationFrame(() => this.renderLoop());
    }

    /**
     * 初始化性能监控
     */
    initStats() {
        if (typeof Stats !== 'undefined') {
            this.stats = new Stats();
            this.stats.showPanel(0); // 0: fps, 1: ms, 2: mb
            document.body.appendChild(this.stats.dom);
        }
    }

    /**
     * 获取场景对象
     * @returns {THREE.Scene} 3D场景对象
     */
    getScene() {
        return this.scene;
    }

    /**
     * 获取渲染器
     * @returns {THREE.WebGLRenderer} WebGL渲染器
     */
    getRenderer() {
        return this.renderer;
    }

    /**
     * 获取相机
     * @returns {THREE.PerspectiveCamera} 相机对象
     */
    getCamera() {
        return this.camera;
    }

    /**
     * 获取相机控制器
     * @returns {CameraController} 相机控制器
     */
    getCameraController() {
        return this.cameraController;
    }

    /**
     * 获取棋盘渲染器
     * @returns {BoardRenderer} 棋盘渲染器
     */
    getBoardRenderer() {
        return this.boardRenderer;
    }

    /**
     * 获取棋子渲染器
     * @returns {PiecesRenderer} 棋子渲染器
     */
    getPiecesRenderer() {
        return this.piecesRenderer;
    }

    /**
     * 获取性能信息
     * @returns {Object} 性能信息
     */
    getPerformanceInfo() {
        return {
            fps: this.fps,
            frameCount: this.frameCount,
            isRendering: this.isRendering,
            rendererInfo: this.renderer.info
        };
    }

    /**
     * 销毁场景管理器
     */
    dispose() {
        this.stopRendering();
        
        // 销毁子组件
        if (this.cameraController) {
            this.cameraController.dispose();
        }
        
        if (this.boardRenderer) {
            this.boardRenderer.dispose();
        }
        
        if (this.piecesRenderer) {
            this.piecesRenderer.dispose();
        }
        
        // 清理场景
        if (this.scene) {
            this.scene.clear();
        }
        
        // 销毁渲染器
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        // 移除性能监控
        if (this.stats && this.stats.dom) {
            document.body.removeChild(this.stats.dom);
        }
        
        // 移除事件监听器
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('visibilitychange', this.pauseRendering);
        
        console.log('Scene manager disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SceneManager;
} else {
    window.SceneManager = SceneManager;
}
