/**
 * HuntGame - 3D棋子渲染器
 * 完整的3D棋子渲染实现
 */

class PiecesRenderer {
    constructor(scene) {
        this.scene = scene;
        this.piecesGroup = new THREE.Group();
        this.pieces = new Map(); // 使用Map存储棋子，key为"row_col"
        this.selectedPiece = null;
        this.animatingPieces = new Set();

        // 材质缓存
        this.materials = {};
        this.geometries = {};

        // 配置
        this.config = GameConfig.pieces;

        // 字体加载器（简化版本，不使用FontLoader）
        this.font = null;

        this.init();
    }

    /**
     * 初始化棋子渲染
     */
    init() {
        this.createMaterials();
        this.createGeometries();
        this.loadFont();

        this.scene.add(this.piecesGroup);
        console.log('Pieces renderer initialized');
    }

    /**
     * 创建材质
     */
    createMaterials() {
        // 红方棋子材质
        this.materials.red = new THREE.MeshStandardMaterial({
            color: this.config.redMaterial.color,
            roughness: this.config.redMaterial.roughness,
            metalness: this.config.redMaterial.metalness,
            side: THREE.DoubleSide
        });

        // 黑方棋子材质
        this.materials.black = new THREE.MeshStandardMaterial({
            color: this.config.blackMaterial.color,
            roughness: this.config.blackMaterial.roughness,
            metalness: this.config.blackMaterial.metalness,
            side: THREE.DoubleSide
        });

        // 文字材质
        this.materials.redText = new THREE.MeshStandardMaterial({
            color: this.config.textColor.red,
            roughness: 0.8,
            metalness: 0.1
        });

        this.materials.blackText = new THREE.MeshStandardMaterial({
            color: this.config.textColor.black,
            roughness: 0.8,
            metalness: 0.1
        });

        // 选中状态材质
        this.materials.selected = new THREE.MeshStandardMaterial({
            color: 0xFFD700,
            roughness: 0.3,
            metalness: 0.7,
            emissive: 0xFFD700,
            emissiveIntensity: 0.2
        });
    }

    /**
     * 创建几何体
     */
    createGeometries() {
        // 基础圆柱体（大部分棋子）
        this.geometries.cylinder = new THREE.CylinderGeometry(
            this.config.radius,
            this.config.radius,
            this.config.height,
            32
        );

        // 王的几何体（稍大一些）
        this.geometries.king = new THREE.CylinderGeometry(
            this.config.radius * 1.1,
            this.config.radius * 1.1,
            this.config.height * 1.2,
            32
        );

        // 兵的几何体（稍小一些）
        this.geometries.pawn = new THREE.CylinderGeometry(
            this.config.radius * 0.9,
            this.config.radius * 0.9,
            this.config.height * 0.9,
            24
        );
    }

    /**
     * 加载字体（简化版本）
     */
    loadFont() {
        // 简化版本，不加载字体文件
        // 使用Canvas 2D API来绘制中文字符
        this.font = null;
        console.log('Font loading skipped (using Canvas 2D API)');
    }

    /**
     * 创建棋子
     * @param {Object} piece 棋子数据
     * @param {number} row 行位置
     * @param {number} col 列位置
     * @returns {THREE.Group} 棋子组
     */
    createPiece(piece, row, col) {
        const pieceGroup = new THREE.Group();
        pieceGroup.name = `piece_${piece.type}_${piece.color}_${row}_${col}`;

        // 选择几何体
        let geometry = this.geometries.cylinder;
        if (piece.type === 'king') {
            geometry = this.geometries.king;
        } else if (piece.type === 'pawn') {
            geometry = this.geometries.pawn;
        }

        // 创建棋子主体
        const material = this.materials[piece.color];
        const pieceMesh = new THREE.Mesh(geometry, material);
        pieceMesh.castShadow = true;
        pieceMesh.receiveShadow = true;
        pieceMesh.name = 'pieceBody';

        pieceGroup.add(pieceMesh);

        // 添加棋子文字
        this.addPieceText(pieceGroup, piece);

        // 设置位置
        const worldPos = MathUtils.boardToWorld(row, col);
        pieceGroup.position.set(worldPos.x, this.config.size.height / 2, worldPos.z);

        // 存储棋子信息
        pieceGroup.userData = {
            piece: piece,
            row: row,
            col: col,
            originalPosition: pieceGroup.position.clone(),
            isSelected: false,
            isAnimating: false
        };

        // 添加到场景和缓存
        this.piecesGroup.add(pieceGroup);
        this.pieces.set(`${row}_${col}`, pieceGroup);

        return pieceGroup;
    }

    /**
     * 添加棋子文字
     * @param {THREE.Group} pieceGroup 棋子组
     * @param {Object} piece 棋子数据
     */
    addPieceText(pieceGroup, piece) {
        // 棋子名称映射
        const pieceNames = {
            'king': piece.color === 'red' ? '帅' : '将',
            'advisor': '士',
            'elephant': piece.color === 'red' ? '相' : '象',
            'horse': '马',
            'chariot': '车',
            'cannon': '炮',
            'pawn': piece.color === 'red' ? '兵' : '卒'
        };

        const text = pieceNames[piece.type] || piece.type;

        if (this.font) {
            // 使用字体创建3D文字
            const textGeometry = new THREE.TextGeometry(text, {
                font: this.font,
                size: 0.15,
                height: 0.02,
                curveSegments: 12,
                bevelEnabled: false
            });

            textGeometry.computeBoundingBox();
            const centerOffsetX = -0.5 * (textGeometry.boundingBox.max.x - textGeometry.boundingBox.min.x);
            const centerOffsetY = -0.5 * (textGeometry.boundingBox.max.y - textGeometry.boundingBox.min.y);

            const textMaterial = this.materials[piece.color + 'Text'];
            const textMesh = new THREE.Mesh(textGeometry, textMaterial);
            textMesh.position.set(centerOffsetX, this.config.size.height / 2 + 0.01, centerOffsetY);
            textMesh.rotation.x = -Math.PI / 2;
            textMesh.name = 'pieceText';

            pieceGroup.add(textMesh);
        } else {
            // 使用简单的几何体表示文字
            this.createSimpleText(pieceGroup, text, piece.color);
        }
    }

    /**
     * 创建简单文字表示
     * @param {THREE.Group} pieceGroup 棋子组
     * @param {string} text 文字
     * @param {string} color 颜色
     */
    createSimpleText(pieceGroup, text, color) {
        // 创建一个小的圆柱体作为文字标记
        const textGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.02, 16);
        const textMaterial = this.materials[color + 'Text'];
        const textMesh = new THREE.Mesh(textGeometry, textMaterial);

        textMesh.position.set(0, this.config.size.height / 2 + 0.02, 0);
        textMesh.name = 'pieceTextSimple';

        pieceGroup.add(textMesh);

        // 可以在这里添加更复杂的文字表示逻辑
        console.log(`Created simple text marker for: ${text}`);
    }

    /**
     * 移除棋子
     * @param {number} row 行位置
     * @param {number} col 列位置
     */
    removePiece(row, col) {
        const key = `${row}_${col}`;
        const pieceGroup = this.pieces.get(key);

        if (pieceGroup) {
            this.piecesGroup.remove(pieceGroup);
            this.pieces.delete(key);

            // 清理几何体和材质
            pieceGroup.traverse((child) => {
                if (child.geometry) {
                    child.geometry.dispose();
                }
            });

            console.log(`Removed piece at ${row}, ${col}`);
        }
    }

    /**
     * 移动棋子
     * @param {number} fromRow 起始行
     * @param {number} fromCol 起始列
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @param {boolean} animated 是否使用动画
     * @returns {Promise} 动画完成的Promise
     */
    async movePiece(fromRow, fromCol, toRow, toCol, animated = true) {
        const fromKey = `${fromRow}_${fromCol}`;
        const toKey = `${toRow}_${toCol}`;
        const pieceGroup = this.pieces.get(fromKey);

        if (!pieceGroup) {
            console.warn(`No piece found at ${fromRow}, ${fromCol}`);
            return;
        }

        // 如果目标位置有棋子，先移除它
        if (this.pieces.has(toKey)) {
            this.removePiece(toRow, toCol);
        }

        // 更新棋子数据
        pieceGroup.userData.row = toRow;
        pieceGroup.userData.col = toCol;

        // 更新缓存
        this.pieces.delete(fromKey);
        this.pieces.set(toKey, pieceGroup);

        if (animated) {
            return this.animateMovePiece(pieceGroup, toRow, toCol);
        } else {
            // 直接移动
            const worldPos = MathUtils.boardToWorld(toRow, toCol);
            pieceGroup.position.set(worldPos.x, this.config.size.height / 2, worldPos.z);
            pieceGroup.userData.originalPosition = pieceGroup.position.clone();
        }
    }

    /**
     * 动画移动棋子
     * @param {THREE.Group} pieceGroup 棋子组
     * @param {number} toRow 目标行
     * @param {number} toCol 目标列
     * @returns {Promise} 动画完成的Promise
     */
    animateMovePiece(pieceGroup, toRow, toCol) {
        return new Promise((resolve) => {
            const startPos = pieceGroup.position.clone();
            const endPos = MathUtils.boardToWorld(toRow, toCol);
            endPos.y = this.config.size.height / 2;

            const duration = this.config.animation.moveDuration;
            const startTime = Date.now();

            pieceGroup.userData.isAnimating = true;
            this.animatingPieces.add(pieceGroup);

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 使用缓动函数
                const easeProgress = MathUtils.easeInOutCubic(progress);

                // 插值位置
                pieceGroup.position.lerpVectors(startPos, endPos, easeProgress);

                // 添加弧形轨迹
                const arcHeight = 0.5;
                const arcOffset = Math.sin(progress * Math.PI) * arcHeight;
                pieceGroup.position.y = endPos.y + arcOffset;

                if (progress >= 1) {
                    // 动画完成
                    pieceGroup.position.copy(endPos);
                    pieceGroup.userData.originalPosition = endPos.clone();
                    pieceGroup.userData.isAnimating = false;
                    this.animatingPieces.delete(pieceGroup);
                    resolve();
                } else {
                    requestAnimationFrame(animate);
                }
            };

            animate();
        });
    }

    /**
     * 选择棋子
     * @param {number} row 行位置
     * @param {number} col 列位置
     */
    selectPiece(row, col) {
        // 取消之前的选择
        if (this.selectedPiece) {
            this.deselectPiece();
        }

        const key = `${row}_${col}`;
        const pieceGroup = this.pieces.get(key);

        if (pieceGroup) {
            this.selectedPiece = pieceGroup;
            pieceGroup.userData.isSelected = true;

            // 改变材质为选中状态
            const pieceMesh = pieceGroup.getObjectByName('pieceBody');
            if (pieceMesh) {
                pieceMesh.material = this.materials.selected;
            }

            // 添加选中动画效果
            this.startSelectionAnimation(pieceGroup);

            console.log(`Selected piece at ${row}, ${col}`);
        }
    }

    /**
     * 取消选择棋子
     */
    deselectPiece() {
        if (this.selectedPiece) {
            this.selectedPiece.userData.isSelected = false;

            // 恢复原始材质
            const pieceMesh = this.selectedPiece.getObjectByName('pieceBody');
            if (pieceMesh) {
                const piece = this.selectedPiece.userData.piece;
                pieceMesh.material = this.materials[piece.color];
            }

            // 停止选中动画
            this.stopSelectionAnimation(this.selectedPiece);

            this.selectedPiece = null;
            console.log('Deselected piece');
        }
    }

    /**
     * 开始选中动画
     * @param {THREE.Group} pieceGroup 棋子组
     */
    startSelectionAnimation(pieceGroup) {
        // 轻微的上下浮动动画
        pieceGroup.userData.selectionAnimation = {
            startY: pieceGroup.position.y,
            time: 0
        };
    }

    /**
     * 停止选中动画
     * @param {THREE.Group} pieceGroup 棋子组
     */
    stopSelectionAnimation(pieceGroup) {
        if (pieceGroup.userData.selectionAnimation) {
            pieceGroup.position.y = pieceGroup.userData.selectionAnimation.startY;
            delete pieceGroup.userData.selectionAnimation;
        }
    }

    /**
     * 获取指定位置的棋子
     * @param {number} row 行位置
     * @param {number} col 列位置
     * @returns {THREE.Group|null} 棋子组
     */
    getPieceAt(row, col) {
        const key = `${row}_${col}`;
        return this.pieces.get(key) || null;
    }

    /**
     * 检查位置是否有棋子
     * @param {number} row 行位置
     * @param {number} col 列位置
     * @returns {boolean} 是否有棋子
     */
    hasPieceAt(row, col) {
        const key = `${row}_${col}`;
        return this.pieces.has(key);
    }

    /**
     * 获取所有棋子
     * @returns {Map} 棋子映射
     */
    getAllPieces() {
        return new Map(this.pieces);
    }

    /**
     * 清除所有棋子
     */
    clearAllPieces() {
        this.pieces.forEach((pieceGroup) => {
            this.piecesGroup.remove(pieceGroup);

            // 清理几何体
            pieceGroup.traverse((child) => {
                if (child.geometry) {
                    child.geometry.dispose();
                }
            });
        });

        this.pieces.clear();
        this.selectedPiece = null;
        this.animatingPieces.clear();

        console.log('Cleared all pieces');
    }

    /**
     * 从棋盘状态创建所有棋子
     * @param {Array} board 棋盘状态
     */
    createPiecesFromBoard(board) {
        this.clearAllPieces();

        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const piece = board[row][col];
                if (piece) {
                    this.createPiece(piece, row, col);
                }
            }
        }

        console.log('Created pieces from board state');
    }

    /**
     * 更新渲染
     * @param {number} deltaTime 时间差
     */
    update(deltaTime) {
        this.updateSelectionAnimations(deltaTime);
        this.updateHoverEffects(deltaTime);
    }

    /**
     * 更新选中动画
     * @param {number} deltaTime 时间差
     */
    updateSelectionAnimations(deltaTime) {
        this.pieces.forEach((pieceGroup) => {
            if (pieceGroup.userData.isSelected && pieceGroup.userData.selectionAnimation) {
                const anim = pieceGroup.userData.selectionAnimation;
                anim.time += deltaTime;

                // 上下浮动效果
                const floatOffset = Math.sin(anim.time * 3) * this.config.animation.hoverHeight;
                pieceGroup.position.y = anim.startY + floatOffset;
            }
        });
    }

    /**
     * 更新悬停效果
     * @param {number} deltaTime 时间差
     */
    updateHoverEffects(deltaTime) {
        // 这里可以添加鼠标悬停时的效果
        // 暂时留空，等待交互系统完成后实现
    }

    /**
     * 设置棋子主题
     * @param {string} theme 主题名称
     */
    setTheme(theme) {
        const themes = {
            classic: {
                red: 0xDC143C,
                black: 0x2F4F4F,
                redText: 0xFFFFFF,
                blackText: 0xFFFFFF
            },
            modern: {
                red: 0xFF6B6B,
                black: 0x4ECDC4,
                redText: 0xFFFFFF,
                blackText: 0x2C3E50
            },
            traditional: {
                red: 0xB22222,
                black: 0x1C1C1C,
                redText: 0xFFD700,
                blackText: 0xFFD700
            }
        };

        const themeConfig = themes[theme];
        if (themeConfig) {
            this.materials.red.color.setHex(themeConfig.red);
            this.materials.black.color.setHex(themeConfig.black);
            this.materials.redText.color.setHex(themeConfig.redText);
            this.materials.blackText.color.setHex(themeConfig.blackText);

            console.log(`Pieces theme changed to: ${theme}`);
        }
    }

    /**
     * 销毁渲染器
     */
    dispose() {
        this.clearAllPieces();

        // 清理几何体
        Object.values(this.geometries).forEach(geometry => {
            geometry.dispose();
        });

        // 清理材质
        Object.values(this.materials).forEach(material => {
            material.dispose();
        });

        // 从场景中移除
        this.scene.remove(this.piecesGroup);

        // 清理引用
        this.pieces.clear();
        this.selectedPiece = null;
        this.animatingPieces.clear();
        this.materials = {};
        this.geometries = {};

        console.log('Pieces renderer disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PiecesRenderer;
} else {
    window.PiecesRenderer = PiecesRenderer;
}
