/**
 * HuntGame - 相机控制模块
 * 管理3D场景中的相机位置、角度和动画
 */

class CameraController {
    constructor(scene, renderer) {
        this.scene = scene;
        this.renderer = renderer;
        this.camera = null;
        this.controls = null;
        
        // 相机状态
        this.isAnimating = false;
        this.animationId = null;
        
        // 预设视角
        this.presets = {
            red: {
                position: { x: 0, y: 12, z: 8 },
                target: { x: 0, y: 0, z: 0 },
                name: '红方视角'
            },
            black: {
                position: { x: 0, y: 12, z: -8 },
                target: { x: 0, y: 0, z: 0 },
                name: '黑方视角'
            },
            overview: {
                position: { x: 0, y: 20, z: 0 },
                target: { x: 0, y: 0, z: 0 },
                name: '俯视视角'
            },
            side: {
                position: { x: 15, y: 8, z: 0 },
                target: { x: 0, y: 0, z: 0 },
                name: '侧面视角'
            }
        };
        
        this.init();
    }

    /**
     * 初始化相机
     */
    init() {
        const config = GameConfig.render.camera;
        const canvas = this.renderer.domElement;
        
        // 创建透视相机
        this.camera = new THREE.PerspectiveCamera(
            config.fov,
            canvas.clientWidth / canvas.clientHeight,
            config.near,
            config.far
        );
        
        // 设置初始位置
        this.camera.position.set(
            config.position.x,
            config.position.y,
            config.position.z
        );
        
        // 设置相机朝向
        this.camera.lookAt(
            config.target.x,
            config.target.y,
            config.target.z
        );
        
        // 创建轨道控制器（用于调试，正式版本中可能会禁用）
        if (GameConfig.debug.enabled && typeof THREE.OrbitControls !== 'undefined') {
            this.controls = new THREE.OrbitControls(this.camera, canvas);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.enableZoom = true;
            this.controls.enablePan = false;
            this.controls.maxPolarAngle = Math.PI / 2;
            this.controls.minDistance = 5;
            this.controls.maxDistance = 30;
        }
        
        console.log('Camera initialized');
    }

    /**
     * 更新相机（每帧调用）
     */
    update() {
        if (this.controls) {
            this.controls.update();
        }
    }

    /**
     * 调整相机宽高比
     */
    resize() {
        const canvas = this.renderer.domElement;
        const aspect = canvas.clientWidth / canvas.clientHeight;
        
        this.camera.aspect = aspect;
        this.camera.updateProjectionMatrix();
    }

    /**
     * 切换到指定预设视角
     * @param {string} presetName 预设名称
     * @param {number} duration 动画持续时间（毫秒）
     * @param {Function} onComplete 完成回调
     */
    switchToPreset(presetName, duration = 1000, onComplete = null) {
        const preset = this.presets[presetName];
        if (!preset) {
            console.warn(`Camera preset '${presetName}' not found`);
            return;
        }
        
        this.animateToPosition(
            preset.position,
            preset.target,
            duration,
            onComplete
        );
    }

    /**
     * 动画移动相机到指定位置
     * @param {Object} targetPosition 目标位置 {x, y, z}
     * @param {Object} targetLookAt 目标朝向 {x, y, z}
     * @param {number} duration 动画持续时间（毫秒）
     * @param {Function} onComplete 完成回调
     */
    animateToPosition(targetPosition, targetLookAt, duration = 1000, onComplete = null) {
        if (this.isAnimating) {
            this.stopAnimation();
        }
        
        this.isAnimating = true;
        
        // 记录起始状态
        const startPosition = {
            x: this.camera.position.x,
            y: this.camera.position.y,
            z: this.camera.position.z
        };
        
        // 计算当前朝向点（简化处理）
        const direction = new THREE.Vector3();
        this.camera.getWorldDirection(direction);
        const distance = this.camera.position.distanceTo(new THREE.Vector3(0, 0, 0));
        const startLookAt = {
            x: this.camera.position.x + direction.x * distance,
            y: this.camera.position.y + direction.y * distance,
            z: this.camera.position.z + direction.z * distance
        };
        
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easedProgress = MathUtils.easeInOut(progress);
            
            // 插值位置
            const currentPosition = {
                x: MathUtils.lerp(startPosition.x, targetPosition.x, easedProgress),
                y: MathUtils.lerp(startPosition.y, targetPosition.y, easedProgress),
                z: MathUtils.lerp(startPosition.z, targetPosition.z, easedProgress)
            };
            
            // 插值朝向
            const currentLookAt = {
                x: MathUtils.lerp(startLookAt.x, targetLookAt.x, easedProgress),
                y: MathUtils.lerp(startLookAt.y, targetLookAt.y, easedProgress),
                z: MathUtils.lerp(startLookAt.z, targetLookAt.z, easedProgress)
            };
            
            // 应用变换
            this.camera.position.set(currentPosition.x, currentPosition.y, currentPosition.z);
            this.camera.lookAt(currentLookAt.x, currentLookAt.y, currentLookAt.z);
            
            if (progress < 1) {
                this.animationId = requestAnimationFrame(animate);
            } else {
                this.isAnimating = false;
                this.animationId = null;
                
                if (onComplete) {
                    onComplete();
                }
            }
        };
        
        animate();
    }

    /**
     * 停止相机动画
     */
    stopAnimation() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        this.isAnimating = false;
    }

    /**
     * 获取相机射线（用于拾取检测）
     * @param {number} x 屏幕x坐标（归一化）
     * @param {number} y 屏幕y坐标（归一化）
     * @returns {THREE.Raycaster} 射线投射器
     */
    getRaycaster(x, y) {
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2(x, y);
        
        raycaster.setFromCamera(mouse, this.camera);
        return raycaster;
    }

    /**
     * 屏幕坐标转世界坐标
     * @param {number} x 屏幕x坐标
     * @param {number} y 屏幕y坐标
     * @param {number} z 深度值（可选）
     * @returns {THREE.Vector3} 世界坐标
     */
    screenToWorld(x, y, z = 0) {
        const canvas = this.renderer.domElement;
        const rect = canvas.getBoundingClientRect();
        
        // 转换为归一化设备坐标
        const mouse = new THREE.Vector2();
        mouse.x = ((x - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((y - rect.top) / rect.height) * 2 + 1;
        
        // 创建向量并反投影
        const vector = new THREE.Vector3(mouse.x, mouse.y, z);
        vector.unproject(this.camera);
        
        return vector;
    }

    /**
     * 世界坐标转屏幕坐标
     * @param {THREE.Vector3} worldPosition 世界坐标
     * @returns {Object} 屏幕坐标 {x, y}
     */
    worldToScreen(worldPosition) {
        const canvas = this.renderer.domElement;
        const vector = worldPosition.clone();
        
        vector.project(this.camera);
        
        return {
            x: (vector.x + 1) / 2 * canvas.clientWidth,
            y: -(vector.y - 1) / 2 * canvas.clientHeight
        };
    }

    /**
     * 设置相机跟随目标
     * @param {THREE.Object3D} target 跟随目标
     * @param {Object} offset 偏移量 {x, y, z}
     */
    followTarget(target, offset = { x: 0, y: 5, z: 5 }) {
        if (!target) return;
        
        const targetPosition = target.position.clone();
        targetPosition.add(new THREE.Vector3(offset.x, offset.y, offset.z));
        
        this.camera.position.copy(targetPosition);
        this.camera.lookAt(target.position);
    }

    /**
     * 获取相机信息
     * @returns {Object} 相机信息
     */
    getInfo() {
        return {
            position: {
                x: this.camera.position.x,
                y: this.camera.position.y,
                z: this.camera.position.z
            },
            rotation: {
                x: this.camera.rotation.x,
                y: this.camera.rotation.y,
                z: this.camera.rotation.z
            },
            fov: this.camera.fov,
            aspect: this.camera.aspect,
            near: this.camera.near,
            far: this.camera.far,
            isAnimating: this.isAnimating
        };
    }

    /**
     * 销毁相机控制器
     */
    dispose() {
        this.stopAnimation();
        
        if (this.controls) {
            this.controls.dispose();
            this.controls = null;
        }
        
        this.camera = null;
        this.scene = null;
        this.renderer = null;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CameraController;
} else {
    window.CameraController = CameraController;
}
