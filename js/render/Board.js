/**
 * HuntGame - 3D棋盘渲染器
 * 完整的3D棋盘渲染实现
 */

class BoardRenderer {
    constructor(scene) {
        this.scene = scene;
        this.boardGroup = new THREE.Group();
        this.boardMesh = null;
        this.gridLines = [];
        this.riverMesh = null;
        this.palaceMeshes = [];
        this.highlightMeshes = [];
        this.validMoveMeshes = [];

        // 材质缓存
        this.materials = {};

        // 配置
        this.config = GameConfig.board;

        this.init();
    }

    /**
     * 初始化棋盘渲染
     */
    init() {
        this.createMaterials();
        this.createBoard();
        this.createGridLines();
        this.createRiver();
        this.createPalaces();
        this.createHighlights();

        this.scene.add(this.boardGroup);
        console.log('Board renderer initialized');
    }

    /**
     * 创建材质
     */
    createMaterials() {
        // 棋盘基础材质
        this.materials.board = new THREE.MeshStandardMaterial({
            color: this.config.material.color,
            roughness: this.config.material.roughness,
            metalness: this.config.material.metalness,
            side: THREE.DoubleSide
        });

        // 网格线材质
        this.materials.gridLine = new THREE.LineBasicMaterial({
            color: this.config.gridColor,
            linewidth: 2,
            transparent: true,
            opacity: 0.8
        });

        // 河界材质
        this.materials.river = new THREE.MeshStandardMaterial({
            color: 0x4682B4,
            roughness: 0.1,
            metalness: 0.3,
            transparent: true,
            opacity: 0.3
        });

        // 九宫格材质
        this.materials.palace = new THREE.LineBasicMaterial({
            color: this.config.palaceColor,
            linewidth: 3,
            transparent: true,
            opacity: 0.9
        });

        // 高亮材质
        this.materials.highlight = new THREE.MeshStandardMaterial({
            color: 0xFFD700,
            transparent: true,
            opacity: 0.6,
            emissive: 0xFFD700,
            emissiveIntensity: 0.2
        });

        // 可移动位置材质
        this.materials.validMove = new THREE.MeshStandardMaterial({
            color: 0x00FF00,
            transparent: true,
            opacity: 0.4,
            emissive: 0x00FF00,
            emissiveIntensity: 0.1
        });
    }

    /**
     * 创建3D棋盘
     */
    createBoard() {
        // 创建棋盘基础几何体
        const boardGeometry = new THREE.BoxGeometry(
            this.config.size.width,
            this.config.size.height,
            this.config.size.depth
        );

        this.boardMesh = new THREE.Mesh(boardGeometry, this.materials.board);
        this.boardMesh.position.set(0, -this.config.size.height / 2, 0);
        this.boardMesh.receiveShadow = true;
        this.boardMesh.name = 'chessboard';

        this.boardGroup.add(this.boardMesh);

        // 添加棋盘纹理（如果有的话）
        if (this.config.texture) {
            this.loadBoardTexture();
        }
    }

    /**
     * 加载棋盘纹理
     */
    loadBoardTexture() {
        const textureLoader = new THREE.TextureLoader();
        textureLoader.load(
            this.config.texture,
            (texture) => {
                texture.wrapS = THREE.RepeatWrapping;
                texture.wrapT = THREE.RepeatWrapping;
                texture.repeat.set(1, 1);

                this.materials.board.map = texture;
                this.materials.board.needsUpdate = true;

                console.log('Board texture loaded');
            },
            undefined,
            (error) => {
                console.warn('Failed to load board texture:', error);
            }
        );
    }

    /**
     * 创建网格线
     */
    createGridLines() {
        const gridGroup = new THREE.Group();
        gridGroup.name = 'gridLines';

        // 横线（10条）
        for (let row = 0; row <= 9; row++) {
            const points = [];
            const y = 0.01; // 略高于棋盘表面
            const z = (row - 4.5) * this.config.gridSpacing;

            // 完整横线
            points.push(new THREE.Vector3(-4 * this.config.gridSpacing, y, z));
            points.push(new THREE.Vector3(4 * this.config.gridSpacing, y, z));

            const geometry = new THREE.BufferGeometry().setFromPoints(points);
            const line = new THREE.Line(geometry, this.materials.gridLine);
            line.name = `horizontalLine_${row}`;
            gridGroup.add(line);
        }

        // 竖线（9条）
        for (let col = 0; col <= 8; col++) {
            const x = (col - 4) * this.config.gridSpacing;
            const y = 0.01;

            // 上半部分（黑方区域）
            let points = [];
            points.push(new THREE.Vector3(x, y, -4.5 * this.config.gridSpacing));
            points.push(new THREE.Vector3(x, y, -0.5 * this.config.gridSpacing));

            let geometry = new THREE.BufferGeometry().setFromPoints(points);
            let line = new THREE.Line(geometry, this.materials.gridLine);
            line.name = `verticalLine_${col}_top`;
            gridGroup.add(line);

            // 下半部分（红方区域）
            points = [];
            points.push(new THREE.Vector3(x, y, 0.5 * this.config.gridSpacing));
            points.push(new THREE.Vector3(x, y, 4.5 * this.config.gridSpacing));

            geometry = new THREE.BufferGeometry().setFromPoints(points);
            line = new THREE.Line(geometry, this.materials.gridLine);
            line.name = `verticalLine_${col}_bottom`;
            gridGroup.add(line);
        }

        this.gridLines = gridGroup.children;
        this.boardGroup.add(gridGroup);
    }

    /**
     * 创建河界
     */
    createRiver() {
        const riverGeometry = new THREE.PlaneGeometry(
            this.config.size.width,
            this.config.gridSpacing
        );

        this.riverMesh = new THREE.Mesh(riverGeometry, this.materials.river);
        this.riverMesh.rotation.x = -Math.PI / 2;
        this.riverMesh.position.set(0, 0.005, 0);
        this.riverMesh.name = 'river';

        this.boardGroup.add(this.riverMesh);

        // 添加河界文字（楚河汉界）
        this.createRiverText();
    }

    /**
     * 创建河界文字
     */
    createRiverText() {
        // 这里可以添加"楚河汉界"文字，暂时用简单的几何体表示
        const textGroup = new THREE.Group();
        textGroup.name = 'riverText';

        // 创建简单的文字标记
        const markerGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.02, 8);
        const markerMaterial = new THREE.MeshStandardMaterial({
            color: 0x8B4513,
            transparent: true,
            opacity: 0.7
        });

        // 左侧标记（楚河）
        const leftMarker = new THREE.Mesh(markerGeometry, markerMaterial);
        leftMarker.position.set(-2, 0.02, 0);
        textGroup.add(leftMarker);

        // 右侧标记（汉界）
        const rightMarker = new THREE.Mesh(markerGeometry, markerMaterial);
        rightMarker.position.set(2, 0.02, 0);
        textGroup.add(rightMarker);

        this.boardGroup.add(textGroup);
    }

    /**
     * 创建九宫格
     */
    createPalaces() {
        const palaceGroup = new THREE.Group();
        palaceGroup.name = 'palaces';

        // 红方九宫格（下方）
        this.createPalace(palaceGroup, 'red', 4.5);

        // 黑方九宫格（上方）
        this.createPalace(palaceGroup, 'black', -4.5);

        this.boardGroup.add(palaceGroup);
    }

    /**
     * 创建单个九宫格
     * @param {THREE.Group} parent 父组
     * @param {string} color 颜色
     * @param {number} centerZ Z轴中心位置
     */
    createPalace(parent, color, centerZ) {
        const y = 0.015; // 略高于网格线
        const spacing = this.config.gridSpacing;

        // 对角线1：左上到右下
        let points = [];
        points.push(new THREE.Vector3(-spacing, y, centerZ - spacing));
        points.push(new THREE.Vector3(spacing, y, centerZ + spacing));

        let geometry = new THREE.BufferGeometry().setFromPoints(points);
        let line = new THREE.Line(geometry, this.materials.palace);
        line.name = `palace_${color}_diagonal1`;
        parent.add(line);

        // 对角线2：右上到左下
        points = [];
        points.push(new THREE.Vector3(spacing, y, centerZ - spacing));
        points.push(new THREE.Vector3(-spacing, y, centerZ + spacing));

        geometry = new THREE.BufferGeometry().setFromPoints(points);
        line = new THREE.Line(geometry, this.materials.palace);
        line.name = `palace_${color}_diagonal2`;
        parent.add(line);

        this.palaceMeshes.push(line);
    }

    /**
     * 创建高亮和提示网格
     */
    createHighlights() {
        // 创建高亮网格（选中棋子位置）
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const highlightGeometry = new THREE.RingGeometry(0.3, 0.4, 16);
                const highlightMesh = new THREE.Mesh(highlightGeometry, this.materials.highlight);

                const worldPos = MathUtils.boardToWorld(row, col);
                highlightMesh.position.set(worldPos.x, 0.02, worldPos.z);
                highlightMesh.rotation.x = -Math.PI / 2;
                highlightMesh.visible = false;
                highlightMesh.name = `highlight_${row}_${col}`;

                this.highlightMeshes.push(highlightMesh);
                this.boardGroup.add(highlightMesh);
            }
        }

        // 创建可移动位置网格
        for (let row = 0; row < 10; row++) {
            for (let col = 0; col < 9; col++) {
                const validMoveGeometry = new THREE.CircleGeometry(0.2, 16);
                const validMoveMesh = new THREE.Mesh(validMoveGeometry, this.materials.validMove);

                const worldPos = MathUtils.boardToWorld(row, col);
                validMoveMesh.position.set(worldPos.x, 0.025, worldPos.z);
                validMoveMesh.rotation.x = -Math.PI / 2;
                validMoveMesh.visible = false;
                validMoveMesh.name = `validMove_${row}_${col}`;

                this.validMoveMeshes.push(validMoveMesh);
                this.boardGroup.add(validMoveMesh);
            }
        }
    }

    /**
     * 高亮指定位置
     * @param {number} row 行
     * @param {number} col 列
     * @param {boolean} visible 是否显示
     */
    highlightPosition(row, col, visible = true) {
        const index = row * 9 + col;
        if (index >= 0 && index < this.highlightMeshes.length) {
            this.highlightMeshes[index].visible = visible;
        }
    }

    /**
     * 显示可移动位置
     * @param {Array} positions 位置数组 [{row, col}, ...]
     */
    showValidMoves(positions) {
        // 隐藏所有可移动位置标记
        this.validMoveMeshes.forEach(mesh => {
            mesh.visible = false;
        });

        // 显示指定位置
        positions.forEach(pos => {
            const index = pos.row * 9 + pos.col;
            if (index >= 0 && index < this.validMoveMeshes.length) {
                this.validMoveMeshes[index].visible = true;
            }
        });
    }

    /**
     * 隐藏所有可移动位置
     */
    hideValidMoves() {
        this.validMoveMeshes.forEach(mesh => {
            mesh.visible = false;
        });
    }

    /**
     * 清除所有高亮
     */
    clearHighlights() {
        this.highlightMeshes.forEach(mesh => {
            mesh.visible = false;
        });
        this.hideValidMoves();
    }

    /**
     * 获取棋盘位置的世界坐标
     * @param {number} row 行
     * @param {number} col 列
     * @returns {THREE.Vector3} 世界坐标
     */
    getBoardPosition(row, col) {
        return MathUtils.boardToWorld(row, col);
    }

    /**
     * 从世界坐标获取棋盘位置
     * @param {THREE.Vector3} worldPos 世界坐标
     * @returns {Object} {row, col} 棋盘位置
     */
    getWorldPosition(worldPos) {
        return MathUtils.worldToBoard(worldPos.x, worldPos.z);
    }

    /**
     * 更新渲染
     * @param {number} deltaTime 时间差
     */
    update(deltaTime) {
        // 更新高亮动画
        this.updateHighlightAnimation(deltaTime);

        // 更新可移动位置动画
        this.updateValidMoveAnimation(deltaTime);
    }

    /**
     * 更新高亮动画
     * @param {number} deltaTime 时间差
     */
    updateHighlightAnimation(deltaTime) {
        const time = Date.now() * 0.001;

        this.highlightMeshes.forEach(mesh => {
            if (mesh.visible) {
                // 脉冲效果
                const pulse = Math.sin(time * 3) * 0.2 + 0.8;
                mesh.material.opacity = pulse * 0.6;
                mesh.material.emissiveIntensity = pulse * 0.3;
            }
        });
    }

    /**
     * 更新可移动位置动画
     * @param {number} deltaTime 时间差
     */
    updateValidMoveAnimation(deltaTime) {
        const time = Date.now() * 0.001;

        this.validMoveMeshes.forEach(mesh => {
            if (mesh.visible) {
                // 缓慢脉冲效果
                const pulse = Math.sin(time * 2) * 0.1 + 0.9;
                mesh.material.opacity = pulse * 0.4;
                mesh.material.emissiveIntensity = pulse * 0.15;

                // 轻微的上下浮动
                const float = Math.sin(time * 4 + mesh.position.x + mesh.position.z) * 0.005;
                mesh.position.y = 0.025 + float;
            }
        });
    }

    /**
     * 设置棋盘主题
     * @param {string} theme 主题名称
     */
    setTheme(theme) {
        const themes = {
            classic: {
                boardColor: 0xDEB887,
                gridColor: 0x8B4513,
                riverColor: 0x4682B4
            },
            modern: {
                boardColor: 0x2F2F2F,
                gridColor: 0xFFFFFF,
                riverColor: 0x00BFFF
            },
            wood: {
                boardColor: 0x8B4513,
                gridColor: 0x654321,
                riverColor: 0x4682B4
            }
        };

        const themeConfig = themes[theme];
        if (themeConfig) {
            this.materials.board.color.setHex(themeConfig.boardColor);
            this.materials.gridLine.color.setHex(themeConfig.gridColor);
            this.materials.river.color.setHex(themeConfig.riverColor);

            console.log(`Board theme changed to: ${theme}`);
        }
    }

    /**
     * 获取棋盘边界框
     * @returns {THREE.Box3} 边界框
     */
    getBoundingBox() {
        const box = new THREE.Box3();
        box.setFromObject(this.boardGroup);
        return box;
    }

    /**
     * 销毁渲染器
     */
    dispose() {
        // 清理几何体和材质
        this.boardGroup.traverse((child) => {
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
        });

        // 从场景中移除
        this.scene.remove(this.boardGroup);

        // 清理引用
        this.boardMesh = null;
        this.riverMesh = null;
        this.gridLines = [];
        this.palaceMeshes = [];
        this.highlightMeshes = [];
        this.validMoveMeshes = [];
        this.materials = {};

        console.log('Board renderer disposed');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BoardRenderer;
} else {
    window.BoardRenderer = BoardRenderer;
}
