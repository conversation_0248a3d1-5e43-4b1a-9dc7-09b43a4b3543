<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HuntGame - 3D中国象棋游戏，支持AI对战和智能提示">
    <meta name="keywords" content="象棋,3D游戏,AI对战,中国象棋,Three.js">
    <title>HuntGame - 3D中国象棋</title>
    
    <!-- 网站图标 -->
    <link rel="icon" href="assets/icons/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="assets/icons/app-icon.png">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#8B4513">
</head>
<body>
    <!-- 加载界面 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <h1>HuntGame</h1>
                <p>3D中国象棋</p>
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <p class="loading-text" id="loading-text">正在加载游戏资源...</p>
            </div>
        </div>
    </div>

    <!-- 游戏主界面 -->
    <div id="game-container" class="game-container" style="display: none;">
        <!-- 3D渲染画布 -->
        <canvas id="game-canvas" class="game-canvas"></canvas>
        
        <!-- 游戏HUD界面 -->
        <div id="game-hud" class="game-hud">
            <!-- 顶部状态栏 -->
            <div class="top-bar">
                <div class="game-info">
                    <span class="current-player" id="current-player">红方回合</span>
                    <span class="game-status" id="game-status">游戏进行中</span>
                </div>
                <div class="game-controls">
                    <button class="btn btn-secondary" id="btn-hint">提示</button>
                    <button class="btn btn-secondary" id="btn-undo">悔棋</button>
                    <button class="btn btn-secondary" id="btn-menu">菜单</button>
                </div>
            </div>
            
            <!-- 侧边控制面板 -->
            <div class="side-panel" id="side-panel">
                <div class="panel-content">
                    <h3>游戏设置</h3>
                    <div class="setting-group">
                        <label>AI难度</label>
                        <select id="ai-difficulty" class="form-select">
                            <option value="easy">简单</option>
                            <option value="medium" selected>中等</option>
                            <option value="hard">困难</option>
                            <option value="expert">专家</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="enable-hints" checked>
                            启用AI提示
                        </label>
                    </div>
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="enable-sound" checked>
                            启用音效
                        </label>
                    </div>
                    <div class="panel-actions">
                        <button class="btn btn-primary" id="btn-new-game">新游戏</button>
                        <button class="btn btn-secondary" id="btn-close-panel">关闭</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 游戏对话框 -->
        <div id="game-dialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content">
                <h3 class="dialog-title" id="dialog-title">提示</h3>
                <p class="dialog-message" id="dialog-message">这是一个对话框消息</p>
                <div class="dialog-actions">
                    <button class="btn btn-primary" id="dialog-confirm">确定</button>
                    <button class="btn btn-secondary" id="dialog-cancel">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 第三方库 -->
    <script src="lib/three.min.js"></script>
    <script src="lib/GLTFLoader.js"></script>
    <script src="lib/OrbitControls.js"></script>
    
    <!-- 工具函数 -->
    <script src="js/utils/math.js"></script>
    <script src="js/utils/storage.js"></script>
    
    <!-- 游戏逻辑模块 -->
    <script src="js/game/Rules.js"></script>
    <script src="js/game/GameState.js"></script>
    <script src="js/game/History.js"></script>
    <script src="js/game/GameRecord.js"></script>
    <script src="js/game/GameReplay.js"></script>
    <script src="js/game/ChessEngine.js"></script>
    
    <!-- AI模块 -->
    <script src="js/ai/Evaluation.js"></script>
    <script src="js/ai/Search.js"></script>
    <script src="js/ai/OpeningBook.js"></script>
    <script src="js/ai/Difficulty.js"></script>
    <script src="js/ai/Hint.js"></script>
    <script src="js/ai/ChessAI.js"></script>
    
    <!-- 3D渲染模块 -->
    <script src="js/render/Camera.js"></script>
    <script src="js/render/Board.js"></script>
    <script src="js/render/Pieces.js"></script>
    <script src="js/render/Scene.js"></script>
    
    <!-- 控制器模块 -->
    <script src="js/controller/InputManager.js"></script>
    <script src="js/controller/AnimationManager.js"></script>
    <script src="js/controller/PieceController.js"></script>
    <script src="js/controller/UIController.js"></script>

    <!-- 音效和特效模块 -->
    <script src="js/audio/AudioManager.js"></script>
    <script src="js/effects/AdvancedAnimationManager.js"></script>
    <script src="js/effects/VisualEffectsManager.js"></script>
    
    <!-- 用户界面模块 -->
    <script src="js/ui/Dialog.js"></script>
    <script src="js/ui/Menu.js"></script>
    <script src="js/ui/HUD.js"></script>
    <script src="js/ui/ResponsiveManager.js"></script>
    <script src="js/ui/HintPanel.js"></script>
    <script src="js/ui/HistoryPanel.js"></script>
    
    <!-- 配置和主入口 -->
    <script src="js/config.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
