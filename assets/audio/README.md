# HuntGame 音频资源说明

## 目录结构

```
assets/audio/
├── sfx/                    # 游戏音效
│   ├── move.mp3           # 移动棋子音效
│   ├── capture.mp3        # 吃子音效
│   ├── check.mp3          # 将军音效
│   ├── checkmate.mp3      # 将死音效
│   ├── select.mp3         # 选择棋子音效
│   ├── invalid.mp3        # 无效操作音效
│   ├── hint.mp3           # 提示音效
│   └── undo.mp3           # 悔棋音效
├── ui/                     # 界面音效
│   ├── click.mp3          # 点击音效
│   ├── hover.mp3          # 悬停音效
│   ├── open.mp3           # 打开面板音效
│   ├── close.mp3          # 关闭面板音效
│   ├── success.mp3        # 成功音效
│   └── error.mp3          # 错误音效
└── music/                  # 背景音乐
    ├── menu.mp3           # 菜单背景音乐
    ├── game.mp3           # 游戏背景音乐
    ├── victory.mp3        # 胜利音乐
    └── defeat.mp3         # 失败音乐
```

## 音频格式要求

- **格式**: MP3 (推荐) 或 OGG
- **采样率**: 44.1kHz
- **比特率**: 128kbps (音效) / 192kbps (音乐)
- **声道**: 立体声
- **时长**: 
  - 音效: 0.5-2秒
  - 音乐: 2-5分钟 (循环)

## 音效设计指南

### 游戏音效 (SFX)
- **move.mp3**: 轻柔的木质撞击声，体现棋子落子
- **capture.mp3**: 略重的撞击声 + 轻微回响，体现吃子
- **check.mp3**: 警告性的铃声或钟声，体现将军
- **checkmate.mp3**: 庄重的钟声或鼓声，体现将死
- **select.mp3**: 轻快的点击声，体现选择
- **invalid.mp3**: 低沉的错误提示音
- **hint.mp3**: 轻柔的提示音，如风铃声
- **undo.mp3**: 反向的移动音效

### 界面音效 (UI)
- **click.mp3**: 清脆的点击声
- **hover.mp3**: 轻微的悬停提示音
- **open.mp3**: 上升音调，体现打开
- **close.mp3**: 下降音调，体现关闭
- **success.mp3**: 愉悦的成功提示音
- **error.mp3**: 低沉的错误提示音

### 背景音乐 (Music)
- **menu.mp3**: 轻松愉快的中国风音乐
- **game.mp3**: 专注思考的背景音乐，不干扰游戏
- **victory.mp3**: 庆祝胜利的音乐
- **defeat.mp3**: 略带遗憾的音乐

## 音频实现特性

### AudioManager 功能
- ✅ Web Audio API 支持
- ✅ 音频预加载和缓存
- ✅ 独立音量控制 (主音量、音效、音乐)
- ✅ 音频上下文管理
- ✅ 循环播放支持
- ✅ 淡入淡出效果
- ✅ 多音效同时播放
- ✅ 音频可视化支持

### 集成特性
- ✅ 与游戏事件自动同步
- ✅ UI交互音效
- ✅ 设置面板音量控制
- ✅ 移动端兼容性
- ✅ 浏览器兼容性检查

## 使用示例

```javascript
// 播放游戏音效
game.playSound('move');
game.playSound('capture', { volume: 0.8 });

// 播放背景音乐
game.playMusic('game', { loop: true, fadeIn: 2 });

// UI音效
game.playUISound('click');
game.playUISound('success');

// 音量控制
game.setSFXVolume(0.7);
game.setMusicVolume(0.5);
game.setAudioEnabled(false);
```

## 音频资源获取建议

### 免费资源
- **Freesound.org**: 高质量音效库
- **Zapsplat**: 专业音效资源
- **Adobe Audition**: 内置音效库
- **GarageBand**: 音乐制作工具

### 中国风音乐
- 使用传统乐器: 古筝、笛子、二胡等
- 五声音阶为主
- 节奏舒缓，适合思考

### 制作工具
- **Audacity**: 免费音频编辑
- **Adobe Audition**: 专业音频处理
- **Logic Pro**: 音乐制作
- **FL Studio**: 电子音乐制作

## 性能优化

### 文件大小控制
- 音效文件 < 100KB
- 音乐文件 < 2MB
- 使用适当的压缩比

### 加载策略
- 关键音效预加载
- 音乐按需加载
- 支持渐进式加载

### 兼容性
- 提供多格式支持 (MP3 + OGG)
- 移动端音频策略
- 低延迟播放优化

## 注意事项

1. **版权问题**: 确保所有音频资源有合法使用权
2. **文件命名**: 使用英文命名，避免特殊字符
3. **音量平衡**: 所有音效音量保持一致
4. **循环音乐**: 确保音乐文件可以无缝循环
5. **移动端**: 考虑移动设备的音频限制

## 当前状态

- ✅ AudioManager 系统完成
- ✅ 音效集成完成
- ⏳ 音频资源待添加
- ⏳ 音量设置界面待完善

添加实际音频文件后，音效系统将完全可用。
