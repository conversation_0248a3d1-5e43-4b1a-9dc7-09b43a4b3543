/**
 * OrbitControls - 简化版本
 * 用于3D场景的轨道控制
 */

THREE.OrbitControls = function(camera, domElement) {
    this.camera = camera;
    this.domElement = domElement || document;
    
    // 控制参数
    this.enabled = true;
    this.target = new THREE.Vector3();
    
    // 旋转参数
    this.enableRotate = true;
    this.rotateSpeed = 1.0;
    
    // 缩放参数
    this.enableZoom = true;
    this.zoomSpeed = 1.0;
    this.minDistance = 0;
    this.maxDistance = Infinity;
    
    // 平移参数
    this.enablePan = true;
    this.panSpeed = 1.0;
    
    // 内部状态
    this.spherical = new THREE.Spherical();
    this.sphericalDelta = new THREE.Spherical();
    this.scale = 1;
    this.panOffset = new THREE.Vector3();
    
    // 鼠标状态
    this.rotateStart = new THREE.Vector2();
    this.rotateEnd = new THREE.Vector2();
    this.rotateDelta = new THREE.Vector2();
    
    this.panStart = new THREE.Vector2();
    this.panEnd = new THREE.Vector2();
    this.panDelta = new THREE.Vector2();
    
    this.zoomStart = new THREE.Vector2();
    this.zoomEnd = new THREE.Vector2();
    this.zoomDelta = new THREE.Vector2();
    
    // 状态枚举
    const STATE = {
        NONE: -1,
        ROTATE: 0,
        DOLLY: 1,
        PAN: 2,
        TOUCH_ROTATE: 3,
        TOUCH_PAN: 4,
        TOUCH_DOLLY_PAN: 5,
        TOUCH_DOLLY_ROTATE: 6
    };
    
    let state = STATE.NONE;
    const scope = this;
    
    // 更新函数
    this.update = function() {
        const offset = new THREE.Vector3();
        const quat = new THREE.Quaternion().setFromUnitVectors(camera.up, new THREE.Vector3(0, 1, 0));
        const quatInverse = quat.clone().invert();
        
        const lastPosition = new THREE.Vector3();
        const lastQuaternion = new THREE.Quaternion();
        
        return function update() {
            const position = camera.position;
            
            offset.copy(position).sub(scope.target);
            offset.applyQuaternion(quat);
            
            scope.spherical.setFromVector3(offset);
            scope.spherical.theta += scope.sphericalDelta.theta;
            scope.spherical.phi += scope.sphericalDelta.phi;
            
            scope.spherical.phi = Math.max(0.000001, Math.min(Math.PI - 0.000001, scope.spherical.phi));
            scope.spherical.radius *= scope.scale;
            scope.spherical.radius = Math.max(scope.minDistance, Math.min(scope.maxDistance, scope.spherical.radius));
            
            scope.target.add(scope.panOffset);
            
            offset.setFromSpherical(scope.spherical);
            offset.applyQuaternion(quatInverse);
            
            position.copy(scope.target).add(offset);
            camera.lookAt(scope.target);
            
            scope.sphericalDelta.set(0, 0, 0);
            scope.scale = 1;
            scope.panOffset.set(0, 0, 0);
            
            if (lastPosition.distanceToSquared(camera.position) > 0.000001 ||
                8 * (1 - lastQuaternion.dot(camera.quaternion)) > 0.000001) {
                
                lastPosition.copy(camera.position);
                lastQuaternion.copy(camera.quaternion);
                
                return true;
            }
            
            return false;
        };
    }();
    
    // 鼠标事件
    function onMouseDown(event) {
        if (!scope.enabled) return;
        
        event.preventDefault();
        
        switch (event.button) {
            case 0: // 左键 - 旋转
                if (!scope.enableRotate) return;
                handleMouseDownRotate(event);
                state = STATE.ROTATE;
                break;
                
            case 1: // 中键 - 缩放
                if (!scope.enableZoom) return;
                handleMouseDownDolly(event);
                state = STATE.DOLLY;
                break;
                
            case 2: // 右键 - 平移
                if (!scope.enablePan) return;
                handleMouseDownPan(event);
                state = STATE.PAN;
                break;
        }
        
        if (state !== STATE.NONE) {
            document.addEventListener('mousemove', onMouseMove, false);
            document.addEventListener('mouseup', onMouseUp, false);
        }
    }
    
    function onMouseMove(event) {
        if (!scope.enabled) return;
        
        event.preventDefault();
        
        switch (state) {
            case STATE.ROTATE:
                if (!scope.enableRotate) return;
                handleMouseMoveRotate(event);
                break;
                
            case STATE.DOLLY:
                if (!scope.enableZoom) return;
                handleMouseMoveDolly(event);
                break;
                
            case STATE.PAN:
                if (!scope.enablePan) return;
                handleMouseMovePan(event);
                break;
        }
    }
    
    function onMouseUp(event) {
        if (!scope.enabled) return;
        
        document.removeEventListener('mousemove', onMouseMove, false);
        document.removeEventListener('mouseup', onMouseUp, false);
        
        state = STATE.NONE;
    }
    
    function onMouseWheel(event) {
        if (!scope.enabled || !scope.enableZoom || (state !== STATE.NONE && state !== STATE.ROTATE)) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        handleMouseWheel(event);
    }
    
    // 鼠标处理函数
    function handleMouseDownRotate(event) {
        scope.rotateStart.set(event.clientX, event.clientY);
    }
    
    function handleMouseDownDolly(event) {
        scope.zoomStart.set(event.clientX, event.clientY);
    }
    
    function handleMouseDownPan(event) {
        scope.panStart.set(event.clientX, event.clientY);
    }
    
    function handleMouseMoveRotate(event) {
        scope.rotateEnd.set(event.clientX, event.clientY);
        scope.rotateDelta.subVectors(scope.rotateEnd, scope.rotateStart).multiplyScalar(scope.rotateSpeed);
        
        const element = scope.domElement === document ? scope.domElement.body : scope.domElement;
        
        scope.sphericalDelta.theta -= 2 * Math.PI * scope.rotateDelta.x / element.clientHeight;
        scope.sphericalDelta.phi -= 2 * Math.PI * scope.rotateDelta.y / element.clientHeight;
        
        scope.rotateStart.copy(scope.rotateEnd);
        scope.update();
    }
    
    function handleMouseMoveDolly(event) {
        scope.zoomEnd.set(event.clientX, event.clientY);
        scope.zoomDelta.subVectors(scope.zoomEnd, scope.zoomStart);
        
        if (scope.zoomDelta.y > 0) {
            scope.scale /= getZoomScale();
        } else if (scope.zoomDelta.y < 0) {
            scope.scale *= getZoomScale();
        }
        
        scope.zoomStart.copy(scope.zoomEnd);
        scope.update();
    }
    
    function handleMouseMovePan(event) {
        scope.panEnd.set(event.clientX, event.clientY);
        scope.panDelta.subVectors(scope.panEnd, scope.panStart).multiplyScalar(scope.panSpeed);
        
        pan(scope.panDelta.x, scope.panDelta.y);
        
        scope.panStart.copy(scope.panEnd);
        scope.update();
    }
    
    function handleMouseWheel(event) {
        if (event.deltaY < 0) {
            scope.scale *= getZoomScale();
        } else if (event.deltaY > 0) {
            scope.scale /= getZoomScale();
        }
        
        scope.update();
    }
    
    // 工具函数
    function getZoomScale() {
        return Math.pow(0.95, scope.zoomSpeed);
    }
    
    function pan(deltaX, deltaY) {
        const element = scope.domElement === document ? scope.domElement.body : scope.domElement;
        
        if (scope.camera.isPerspectiveCamera) {
            const position = scope.camera.position;
            const offset = position.clone().sub(scope.target);
            let targetDistance = offset.length();
            
            targetDistance *= Math.tan((scope.camera.fov / 2) * Math.PI / 180.0);
            
            panLeft(2 * deltaX * targetDistance / element.clientHeight, scope.camera.matrix);
            panUp(2 * deltaY * targetDistance / element.clientHeight, scope.camera.matrix);
            
        } else if (scope.camera.isOrthographicCamera) {
            panLeft(deltaX * (scope.camera.right - scope.camera.left) / scope.camera.zoom / element.clientWidth, scope.camera.matrix);
            panUp(deltaY * (scope.camera.top - scope.camera.bottom) / scope.camera.zoom / element.clientHeight, scope.camera.matrix);
        }
    }
    
    function panLeft(distance, objectMatrix) {
        const v = new THREE.Vector3();
        v.setFromMatrixColumn(objectMatrix, 0);
        v.multiplyScalar(-distance);
        scope.panOffset.add(v);
    }
    
    function panUp(distance, objectMatrix) {
        const v = new THREE.Vector3();
        v.setFromMatrixColumn(objectMatrix, 1);
        v.multiplyScalar(distance);
        scope.panOffset.add(v);
    }
    
    function onContextMenu(event) {
        if (!scope.enabled) return;
        event.preventDefault();
    }
    
    // 事件处理
    this.dispose = function() {
        scope.domElement.removeEventListener('contextmenu', onContextMenu, false);
        scope.domElement.removeEventListener('mousedown', onMouseDown, false);
        scope.domElement.removeEventListener('wheel', onMouseWheel, false);
        
        document.removeEventListener('mousemove', onMouseMove, false);
        document.removeEventListener('mouseup', onMouseUp, false);
    };
    
    // 绑定事件
    this.domElement.addEventListener('contextmenu', onContextMenu, false);
    this.domElement.addEventListener('mousedown', onMouseDown, false);
    this.domElement.addEventListener('wheel', onMouseWheel, false);
    
    // 初始化
    this.update();
};

console.log('OrbitControls loaded');
