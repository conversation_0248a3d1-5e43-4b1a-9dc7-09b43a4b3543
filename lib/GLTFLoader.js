/**
 * GLTFLoader - 简化版本
 * 用于加载GLTF模型文件
 */

THREE.GLTFLoader = function() {
    this.load = function(url, onLoad, onProgress, onError) {
        console.log('GLTFLoader: Loading', url);
        
        // 简化版本，暂时返回空对象
        if (onLoad) {
            setTimeout(() => {
                onLoad({
                    scene: new THREE.Group(),
                    scenes: [],
                    cameras: [],
                    animations: []
                });
            }, 100);
        }
    };
};

console.log('GLTFLoader loaded');
